import { sequelize } from '../config/database.js';
import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import { fileURLToPath } from 'url';

const execPromise = promisify(exec);
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '../../');

async function fixDatabase() {
  try {
    console.log('Starting database fix...');
    
    // Run the migration using sequelize-cli
    const { stdout, stderr } = await execPromise(
      'npx sequelize-cli db:migrate --name 20240605000000-fix-grammar-topics-id.js',
      { cwd: rootDir }
    );
    
    if (stderr) {
      console.error('Migration error:', stderr);
    }
    
    console.log('Migration output:', stdout);
    console.log('Database fix completed successfully');
    
    process.exit(0);
  } catch (error) {
    console.error('Failed to fix database:', error);
    process.exit(1);
  }
}

fixDatabase(); 