<template>
  <div class="max-w-3xl mx-auto px-4 py-8">
    <div v-if="isLoading" class="text-center py-8 text-gray-600 dark:text-gray-400">
      Loading exercises...
    </div>
    
    <div v-else-if="error" class="text-center p-4 text-red-600 dark:text-red-400">
      {{ error }}
      <button @click="fetchExercises" class="mt-4 px-4 py-2 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors dark:text-gray-200">Retry</button>
    </div>
    
    <div v-else>
      <div class="mb-8">
        <router-link :to="`/lessons/${lessonId}`" class="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
          &larr; Back to Lesson
        </router-link>
      </div>
      
      <h1 class="text-3xl font-bold mb-6 text-gray-900 dark:text-white">Practice Exercises</h1>
      
      <div v-if="exercises.length === 0" class="text-center p-8 bg-gray-50 dark:bg-gray-800 rounded-lg text-gray-600 dark:text-gray-400">
        No exercises available for this lesson yet.
      </div>
      
      <div v-else>
        <div class="mb-8">
          <div class="font-bold mb-2 text-gray-800 dark:text-gray-200">
            Exercise {{ currentExerciseIndex + 1 }} of {{ exercises.length }}
          </div>
          <div class="h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
            <div 
              class="h-full bg-green-500 transition-all duration-300" 
              :style="{ width: `${(currentExerciseIndex + 1) / exercises.length * 100}%` }"
            ></div>
          </div>
        </div>
        
        <div class="p-8 bg-white dark:bg-dark-card rounded-lg shadow-md">
          <h3 class="text-xl font-semibold mb-6 text-gray-800 dark:text-gray-200">{{ currentExercise.question }}</h3>
          
          <!-- Multiple choice -->
          <div v-if="currentExercise.type === 'multiple-choice'" class="flex flex-col space-y-4 mb-6">
            <div 
              v-for="(option, index) in currentExercise.options" 
              :key="index"
              class="p-4 border rounded-md cursor-pointer transition-all duration-200 dark:text-gray-200"
              :class="{ 
                'border-blue-500 bg-blue-50 dark:bg-blue-900/30': userAnswer === option && !showFeedback,
                'border-green-500 bg-green-50 dark:bg-green-900/30': showFeedback && option === currentExercise.correctAnswer,
                'border-red-500 bg-red-50 dark:bg-red-900/30': showFeedback && userAnswer === option && option !== currentExercise.correctAnswer,
                'hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-700': !showFeedback && userAnswer !== option
              }"
              @click="selectAnswer(option)"
            >
              {{ option }}
            </div>
          </div>
          
          <!-- Fill in the blank -->
          <div v-else-if="currentExercise.type === 'fill-blank'" class="mb-6">
            <input 
              type="text" 
              v-model="userAnswer" 
              placeholder="Type your answer here"
              :disabled="showFeedback"
              class="w-full p-4 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 dark:bg-gray-700 dark:text-gray-200 dark:disabled:bg-gray-800"
            />
          </div>
          
          <!-- True/False -->
          <div v-else-if="currentExercise.type === 'true-false'" class="flex flex-col space-y-4 mb-6">
            <div 
              class="p-4 border rounded-md cursor-pointer transition-all duration-200 dark:text-gray-200"
              :class="{ 
                'border-blue-500 bg-blue-50 dark:bg-blue-900/30': userAnswer === 'true' && !showFeedback,
                'border-green-500 bg-green-50 dark:bg-green-900/30': showFeedback && 'true' === currentExercise.correctAnswer,
                'border-red-500 bg-red-50 dark:bg-red-900/30': showFeedback && userAnswer === 'true' && 'true' !== currentExercise.correctAnswer,
                'hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-700': !showFeedback && userAnswer !== 'true'
              }"
              @click="selectAnswer('true')"
            >
              True
            </div>
            <div 
              class="p-4 border rounded-md cursor-pointer transition-all duration-200 dark:text-gray-200"
              :class="{ 
                'border-blue-500 bg-blue-50 dark:bg-blue-900/30': userAnswer === 'false' && !showFeedback,
                'border-green-500 bg-green-50 dark:bg-green-900/30': showFeedback && 'false' === currentExercise.correctAnswer,
                'border-red-500 bg-red-50 dark:bg-red-900/30': showFeedback && userAnswer === 'false' && 'false' !== currentExercise.correctAnswer,
                'hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-700': !showFeedback && userAnswer !== 'false'
              }"
              @click="selectAnswer('false')"
            >
              False
            </div>
          </div>
          
          <div v-if="showFeedback" class="my-6 p-4 rounded-md" 
               :class="isCorrect ? 'bg-green-50 dark:bg-green-900/20 border-l-4 border-green-500' : 'bg-red-50 dark:bg-red-900/20 border-l-4 border-red-500'">
            <div class="font-bold mb-2 text-gray-800 dark:text-gray-200">
              {{ isCorrect ? 'Correct!' : 'Incorrect!' }}
            </div>
            <div class="text-gray-700 dark:text-gray-300">
              {{ currentExercise.explanation }}
            </div>
          </div>
          
          <div class="flex justify-center mt-8">
            <button 
              v-if="!showFeedback" 
              @click="checkAnswer" 
              class="px-6 py-3 bg-blue-500 text-white font-bold rounded-md hover:bg-blue-600 transition-colors disabled:bg-gray-300 dark:disabled:bg-gray-700 disabled:cursor-not-allowed"
              :disabled="!userAnswer"
            >
              Check Answer
            </button>
            
            <button 
              v-else 
              @click="nextExercise" 
              class="px-6 py-3 bg-green-500 text-white font-bold rounded-md hover:bg-green-600 transition-colors"
            >
              {{ isLastExercise ? 'Finish' : 'Next Exercise' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { grammarService } from '../../services/grammar.service';

const route = useRoute();
const router = useRouter();

interface Exercise {
  id: string;
  question: string;
  options?: string[];
  correctAnswer: string;
  explanation: string;
  lessonId: string;
  type: string;
  difficulty: string;
}

const exercises = ref<Exercise[]>([]);
const currentExerciseIndex = ref(0);
const userAnswer = ref('');
const showFeedback = ref(false);
const isCorrect = ref(false);
const isLoading = ref(true);
const error = ref('');

const lessonId = computed(() => {
  return route.params.id as string;
});

const currentExercise = computed<Exercise>(() => {
  return exercises.value[currentExerciseIndex.value] || {} as Exercise;
});

const isLastExercise = computed(() => {
  return currentExerciseIndex.value === exercises.value.length - 1;
});

const fetchExercises = async () => {
  error.value = '';
  isLoading.value = true;
  
  try {
    exercises.value = await grammarService.getExercisesByLesson(lessonId.value);
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to load exercises.';
  } finally {
    isLoading.value = false;
  }
};

const selectAnswer = (answer: string) => {
  if (showFeedback.value) return;
  userAnswer.value = answer;
};

const checkAnswer = async () => {
  if (!currentExercise.value || !currentExercise.value.id) return;
  
  try {
    const result = await grammarService.validateAnswer(currentExercise.value.id, { answer: userAnswer.value });
    isCorrect.value = result.isCorrect;
    showFeedback.value = true;
    
    // If user is authenticated, submit the answer to track progress
    try {
      await grammarService.submitExerciseAnswer(currentExercise.value.id, { answer: userAnswer.value });
    } catch (err) {
      // Silently fail if user is not authenticated
    }
  } catch (err) {
    // Handle error
  }
};

const nextExercise = () => {
  if (isLastExercise.value) {
    // Navigate back to lesson
    router.push(`/lessons/${lessonId.value}`);
  } else {
    // Move to next exercise
    currentExerciseIndex.value++;
    userAnswer.value = '';
    showFeedback.value = false;
    isCorrect.value = false;
  }
};

onMounted(fetchExercises);
</script> 