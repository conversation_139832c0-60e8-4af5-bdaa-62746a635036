<template>
  <div class="flex flex-col min-h-screen bg-white dark:bg-dark text-gray-800 dark:text-dark-primary transition-colors duration-300">
    <HeaderComponent />
    <main class="flex-1 py-8">
      <slot></slot>
    </main>
    <FooterComponent />
  </div>
</template>

<script setup lang="ts">
import HeaderComponent from './HeaderComponent.vue';
import FooterComponent from './FooterComponent.vue';
import { useThemeStore } from '../../stores/theme';

// Khởi tạo theme store để đảm bảo theme được áp dụng khi ứng dụng khởi động
const themeStore = useThemeStore();

defineOptions({
  name: 'MainLayout'
});
</script> 