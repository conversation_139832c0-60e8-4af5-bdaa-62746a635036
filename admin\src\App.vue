<script setup>
import { RouterView } from 'vue-router'
import { useAuthStore } from './stores/auth'
import { computed } from 'vue'

const authStore = useAuthStore()
const isAuthenticated = computed(() => authStore.isAuthenticated)
</script>

<template>
  <!-- The app simply renders the current route view -->
  <RouterView />
</template>

<style>
/* Reset and global styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  line-height: 1.5;
}

/* Global form controls */
input, select, textarea, button {
  font-family: inherit;
}

button {
  cursor: pointer;
}

/* Dark mode input and control overrides */
.dark-mode input,
.dark-mode select,
.dark-mode textarea {
  background-color: #111827;
  color: #f9fafb;
  border-color: #374151;
}

.dark-mode button:not(.theme-toggle):not(.logout-button) {
  background-color: #374151;
  color: #f9fafb;
}

.dark-mode a {
  color: #93c5fd;
}
</style>
