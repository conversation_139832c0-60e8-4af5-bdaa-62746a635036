import sqlite3 from 'sqlite3';
const db = new sqlite3.Database('./database.sqlite');

db.all(`
  SELECT 
    gt.id, 
    gt.name, 
    COUNT(DISTINCT l.id) as lesson_count, 
    COUNT(e.id) as exercise_count 
  FROM grammar_topics gt 
  LEFT JOIN lessons l ON gt.id = l.topicId 
  LEFT JOIN exercises e ON l.id = e.lessonId 
  GROUP BY gt.id, gt.name
`, [], (err, rows) => {
  if (err) {
    console.error('Error:', err);
  } else {
    console.log('Topic exercise counts:');
    rows.forEach(row => {
      console.log(`${row.id}. ${row.name}: ${row.lesson_count} lessons, ${row.exercise_count} exercises`);
    });
    
    // Check if any topics have no exercises
    const topicsWithoutExercises = rows.filter(row => row.exercise_count === 0);
    if (topicsWithoutExercises.length > 0) {
      console.log('\nTopics without exercises:');
      topicsWithoutExercises.forEach(topic => {
        console.log(`- ${topic.name}`);
      });
    } else {
      console.log('\nAll topics have exercises!');
    }
  }
  db.close();
}); 