import { DataTypes, Model } from 'sequelize';
import { sequelize } from '../config/database.js';

class LearningPath extends Model {}

LearningPath.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  level: {
    type: DataTypes.ENUM('beginner', 'intermediate', 'advanced'),
    allowNull: false,
    defaultValue: 'beginner'
  },
  estimatedDuration: {
    type: DataTypes.INTEGER, // in hours
    allowNull: false,
    defaultValue: 10
  },
  topics: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: 'JSON array of topic IDs in sequence'
  },
  imageUrl: {
    type: DataTypes.STRING,
    allowNull: true
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  createdAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updatedAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  sequelize,
  modelName: 'LearningPath',
  tableName: 'learning_paths',
  timestamps: true
});

export default LearningPath; 