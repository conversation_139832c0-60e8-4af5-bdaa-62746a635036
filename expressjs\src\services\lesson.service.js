import BaseService from './base.service.js';
import { lessonRepository, userProgressRepository } from '../repositories/index.js';

class LessonService extends BaseService {
  constructor() {
    super(lessonRepository);
  }

  /**
   * Get lesson with its exercises
   * @param {number} id - Lesson ID
   * @returns {Promise<Object>} - Lesson with exercises
   */
  async getLessonWithExercises(id) {
    const lesson = await this.repository.findWithExercises(id);
    if (!lesson) {
      throw new Error(`Lesson with id ${id} not found`);
    }
    return lesson;
  }

  /**
   * Get lessons by topic ID
   * @param {number} topicId - Topic ID
   * @returns {Promise<Array>} - Array of lessons
   */
  async getLessonsByTopic(topicId) {
    return this.repository.findByTopicId(topicId);
  }

  /**
   * Mark lesson as completed for user
   * @param {number} lessonId - Lesson ID
   * @param {number} userId - User ID
   * @returns {Promise<Object>} - Updated user progress
   */
  async markLessonCompleted(lessonId, userId) {
    // Verify lesson exists
    const lesson = await this.repository.findById(lessonId);
    if (!lesson) {
      throw new Error(`Lesson with id ${lessonId} not found`);
    }
    
    // Update or create user progress record
    const [userProgress, created] = await userProgressRepository.findOrCreateLessonProgress(
      userId, 
      lessonId,
      {
        status: 'completed',
        completedAt: new Date(),
        lastAccessedAt: new Date()
      }
    );
    
    if (!created) {
      userProgress.status = 'completed';
      userProgress.completedAt = new Date();
      userProgress.lastAccessedAt = new Date();
      await userProgress.save();
    }
    
    return userProgress;
  }
}

export default new LessonService(); 