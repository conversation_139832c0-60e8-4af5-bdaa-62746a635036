import { DataTypes, Model } from 'sequelize';
import { sequelize } from '../config/database.js';

class UserLearningPath extends Model {}

UserLearningPath.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  learningPathId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'learning_paths',
      key: 'id'
    }
  },
  currentTopicIndex: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  progress: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0,
      max: 100
    }
  },
  startedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  completedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  createdAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updatedAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  sequelize,
  modelName: 'UserLearningPath',
  tableName: 'user_learning_paths',
  timestamps: true,
  indexes: [
    {
      name: 'user_learning_path_idx',
      fields: ['userId', 'learningPathId'],
      unique: true
    }
  ]
});

export default UserLearningPath; 