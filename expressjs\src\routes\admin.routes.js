import express from 'express';
import { adminController } from '../controllers/index.js';
import { AuthMiddleware } from '../middlewares/index.js';

const router = express.Router();

// Admin auth routes
router.post('/login', adminController.login);
router.post('/logout', adminController.logout);

// Protected admin routes - requires admin role
router.get('/profile', AuthMiddleware.authenticate, AuthMiddleware.requireAdmin, adminController.getProfile);

export default router; 