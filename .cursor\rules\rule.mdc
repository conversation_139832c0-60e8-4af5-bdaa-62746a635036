---
description: 
globs: 
alwaysApply: true
---
1. Mỗi khi bạn xử lý vấn đề gì từ dự án expressjs thì bạn phải luôn tuân thủ theo cấu trúc và base của dự án và đặc biệt là phải tuân theo mô hình repository và service dự án của tôi
2. Mỗi khi bạn xử lý vấn đề gì liên quan đến chức năng của 1 view bạn phải đảm bảo luôn chia chức năng đó vào trong thư mục component riêng bên trong view đó
3. Hãy nhớ mỗi lần mà bạn chạy cmd thì bạn phải luôn dùng lệnh của powershell chứ không được dùng dấu &&
4. Đặc biệt trong dự án vuejs bắt buộc bạn phải dùng script setup
5. Đ<PERSON><PERSON> bảo mỗi khi bạn xử lý vấn đề gọi api ở trên dự án admin hoặc vuejs thì bạn phải đưa link api vào env

