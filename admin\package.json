{"name": "admin", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"@primevue/themes": "^4.3.5", "axios": "^1.10.0", "chart.js": "^4.5.0", "pinia": "^3.0.1", "primeflex": "^4.0.0", "primeicons": "^7.0.0", "primevue": "^4.3.5", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "npm-run-all2": "^7.0.2", "postcss": "^8.5.5", "tailwindcss": "^4.1.10", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}