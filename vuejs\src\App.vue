<script setup lang="ts">
import { RouterView } from 'vue-router'
import { onMounted } from 'vue'
import MainLayout from './components/layout/MainLayout.vue'
import { useAuthStore } from './stores/auth'

const authStore = useAuthStore()

onMounted(async () => {
  try {
    // Always attempt to fetch user data regardless of token status
    // This will help with debugging authentication issues
    await authStore.fetchCurrentUser()
  } catch (error) {
    console.error('Failed to fetch user data:', error)
  }
})
</script>

<template>
  <MainLayout>
    <RouterView />
  </MainLayout>
</template>
