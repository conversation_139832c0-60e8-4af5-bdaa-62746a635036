import express from 'express';
import userRoutes from './user.routes.js';
import fileRoutes from './file.routes.js';
import authRoutes from './auth.routes.js';
import grammarRoutes from './grammar.routes.js';
import adminRoutes from './admin.routes.js';

const router = express.Router();

// Health check route
router.get('/health', (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'API is up and running'
  });
});

// API routes
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/files', fileRoutes);
router.use('/grammar', grammarRoutes);
router.use('/admin', adminRoutes);

export default router;
