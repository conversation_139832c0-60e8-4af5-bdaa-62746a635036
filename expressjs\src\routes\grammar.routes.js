import express from 'express';
import { grammarTopic<PERSON>ontroller, lessonController, userProgressController, exerciseController } from '../controllers/index.js';
import { AuthMiddleware } from '../middlewares/index.js';

const router = express.Router();

// Public routes - available to all users
router.get('/topics', grammarTopicController.getAllTopics);
router.get('/topics/level/:level', grammarTopicController.getTopicsByLevel);

// Routes that require authentication
router.use('/topics/:id', AuthMiddleware.authenticate);
router.get('/topics/:id', grammarTopicController.getTopicWithLessons);

router.use('/lessons', AuthMiddleware.authenticate);
router.get('/lessons/topic/:topicId', lessonController.getLessonsByTopic);
router.get('/lessons/:id', lessonController.getLessonWithExercises);

router.use('/exercises', AuthMiddleware.authenticate);
router.get('/exercises/lesson/:lessonId', exerciseController.getExercisesByLesson);
router.get('/exercises/difficulty/:difficulty', exerciseController.getExercisesByDifficulty);
router.post('/exercises/:id/validate', exerciseController.validateAnswer);

// Protected routes for user progress
router.post('/lessons/:id/complete', AuthMiddleware.authenticate, lessonController.markLessonCompleted);

// Đảm bảo middleware xác thực cho route progress
router.get('/progress', AuthMiddleware.authenticate, userProgressController.getUserProgress);
router.post('/exercises/:exerciseId/submit', AuthMiddleware.authenticate, userProgressController.submitExerciseAnswer);

export default router; 