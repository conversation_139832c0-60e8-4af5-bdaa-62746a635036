import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import LoginView from '../views/LoginView.vue'
import ExampleComponents from '../components/ExampleComponents.vue'
import DefaultLayout from '../layouts/DefaultLayout.vue'

// Authentication guard
const requireAuth = (to: any, from: any, next: any) => {
  const adminUser = localStorage.getItem('adminUser');
  const adminToken = localStorage.getItem('adminAccessToken');
  
  if (!adminUser || !adminToken) {
    next({ name: 'login' });
  } else {
    next();
  }
};

// Guest guard (for login page)
const requireGuest = (to: any, from: any, next: any) => {
  const adminUser = localStorage.getItem('adminUser');
  const adminToken = localStorage.getItem('adminAccessToken');
  
  if (adminUser && adminToken) {
    next({ name: 'home' });
  } else {
    next();
  }
};

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: DefaultLayout,
      beforeEnter: requireAuth,
      children: [
        {
          path: '',
          name: 'home',
          component: HomeView,
        },
        {
          path: 'components',
          name: 'components',
          component: ExampleComponents,
        }
      ]
    },
    {
      path: '/login',
      name: 'login',
      component: LoginView,
      beforeEnter: requireGuest
    }
  ],
})

export default router
