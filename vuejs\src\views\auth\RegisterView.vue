<template>
  <div class="flex justify-center items-center min-h-[calc(100vh-200px)] py-8 px-4">
    <div class="w-full max-w-lg bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden">
      <div class="p-8 bg-gradient-to-r from-blue-600 to-blue-700 text-white text-center">
        <h1 class="text-2xl font-bold mb-2">Create Account</h1>
        <p class="opacity-80">Join our community to improve your English grammar skills</p>
      </div>
      
      <form @submit.prevent="handleRegister" class="p-8">
        <div class="mb-6">
          <label for="name" class="flex items-center mb-2 font-medium text-gray-700 dark:text-gray-300">
            <span class="mr-2 text-primary">👤</span>
            Full Name
          </label>
          <input 
            type="text" 
            id="name" 
            v-model="name" 
            required 
            placeholder="Enter your full name"
            autocomplete="name"
            class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
            :class="{'border-red-500 dark:border-red-400': validationErrors.name, 'border-gray-300 dark:border-gray-600': !validationErrors.name}"
            @blur="validateName"
          />
          <p v-if="validationErrors.name" class="mt-1 text-sm text-red-500 dark:text-red-400">{{ validationErrors.name }}</p>
        </div>
        
        <div class="mb-6">
          <label for="username" class="flex items-center mb-2 font-medium text-gray-700 dark:text-gray-300">
            <span class="mr-2 text-primary">@</span>
            Username
          </label>
          <input 
            type="text" 
            id="username" 
            v-model="username" 
            required 
            placeholder="Choose a username"
            autocomplete="username"
            class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
            :class="{'border-red-500 dark:border-red-400': validationErrors.username, 'border-gray-300 dark:border-gray-600': !validationErrors.username}"
            @blur="validateUsername"
          />
          <p v-if="validationErrors.username" class="mt-1 text-sm text-red-500 dark:text-red-400">{{ validationErrors.username }}</p>
        </div>
        
        <div class="mb-6">
          <label for="email" class="flex items-center mb-2 font-medium text-gray-700 dark:text-gray-300">
            <span class="mr-2 text-primary">✉️</span>
            Email
          </label>
          <input 
            type="email" 
            id="email" 
            v-model="email" 
            required 
            placeholder="Enter your email"
            autocomplete="email"
            class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
            :class="{'border-red-500 dark:border-red-400': validationErrors.email, 'border-gray-300 dark:border-gray-600': !validationErrors.email}"
            @blur="validateEmail"
          />
          <p v-if="validationErrors.email" class="mt-1 text-sm text-red-500 dark:text-red-400">{{ validationErrors.email }}</p>
        </div>
        
        <div class="mb-6 relative">
          <label for="password" class="flex items-center mb-2 font-medium text-gray-700 dark:text-gray-300">
            <span class="mr-2 text-primary">🔒</span>
            Password
          </label>
          <div class="relative">
            <input 
              :type="showPassword ? 'text' : 'password'" 
              id="password" 
              v-model="password" 
              required 
              placeholder="Enter your password"
              autocomplete="new-password"
              class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
              :class="{'border-red-500 dark:border-red-400': validationErrors.password, 'border-gray-300 dark:border-gray-600': !validationErrors.password}"
              @blur="validatePassword"
            />
            <button 
              type="button" 
              class="absolute right-3 top-1/2 -translate-y-1/2 bg-transparent border-none text-gray-500 dark:text-gray-400 cursor-pointer p-0"
              @click="showPassword = !showPassword"
            >
              <span>{{ showPassword ? '👁️‍🗨️' : '👁️' }}</span>
            </button>
          </div>
          <p v-if="validationErrors.password" class="mt-1 text-sm text-red-500 dark:text-red-400">{{ validationErrors.password }}</p>
          <div class="mt-2" v-if="password">
            <div class="h-1 bg-gray-200 dark:bg-gray-600 rounded-sm overflow-hidden">
              <div 
                class="h-full transition-all duration-300"
                :style="{ width: passwordStrength + '%' }"
                :class="{
                  'bg-red-500': passwordStrengthClass === 'weak',
                  'bg-yellow-500': passwordStrengthClass === 'medium',
                  'bg-green-500': passwordStrengthClass === 'strong'
                }"
              ></div>
            </div>
            <span class="block text-xs mt-1 text-right"
              :class="{
                'text-red-500': passwordStrengthClass === 'weak',
                'text-yellow-500': passwordStrengthClass === 'medium',
                'text-green-500': passwordStrengthClass === 'strong'
              }"
            >
              {{ passwordStrengthText }}
            </span>
          </div>
        </div>
        
        <div v-if="error" class="bg-red-50 dark:bg-red-900/30 text-red-600 dark:text-red-400 p-3 rounded-lg mb-6 flex items-center">
          <span class="mr-2">⚠️</span>
          {{ error }}
        </div>
        
        <button 
          type="submit" 
          class="w-full py-3 bg-primary text-white font-bold rounded-lg transition-colors duration-300 hover:bg-primary-dark disabled:bg-gray-300 disabled:cursor-not-allowed flex justify-center items-center"
          :disabled="isLoading || !isFormValid"
        >
          <span v-if="isLoading" class="flex items-center">
            <span class="mr-2 animate-spin inline-block">⏳</span>
            Creating account...
          </span>
          <span v-else>Create Account</span>
        </button>
        
        <div class="text-center mt-6 text-gray-500 dark:text-gray-400">
          Already have an account? 
          <router-link to="/login" class="text-primary font-medium hover:text-primary-dark">Sign in</router-link>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { authService } from '../../services/auth.service';
import { toastService } from '../../services/toast.service';

const router = useRouter();

const name = ref('');
const username = ref('');
const email = ref('');
const password = ref('');
const error = ref('');
const isLoading = ref(false);
const showPassword = ref(false);

// Validation errors
const validationErrors = reactive({
  name: '',
  username: '',
  email: '',
  password: ''
});

// Validation functions
const validateName = () => {
  validationErrors.name = '';
  if (name.value.trim() === '') {
    validationErrors.name = 'Name is required';
  } else if (name.value.trim().length < 2) {
    validationErrors.name = 'Name must be at least 2 characters';
  }
};

const validateUsername = () => {
  validationErrors.username = '';
  if (username.value.trim() === '') {
    validationErrors.username = 'Username is required';
  } else if (username.value.trim().length < 3) {
    validationErrors.username = 'Username must be at least 3 characters';
  } else if (!/^[a-zA-Z0-9_]+$/.test(username.value)) {
    validationErrors.username = 'Username can only contain letters, numbers and underscores';
  }
};

const validateEmail = () => {
  validationErrors.email = '';
  if (email.value.trim() === '') {
    validationErrors.email = 'Email is required';
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.value)) {
    validationErrors.email = 'Please enter a valid email address';
  }
};

const validatePassword = () => {
  validationErrors.password = '';
  if (password.value.trim() === '') {
    validationErrors.password = 'Password is required';
  } else if (password.value.length < 6) {
    validationErrors.password = 'Password must be at least 6 characters';
  }
};

const validateAll = () => {
  validateName();
  validateUsername();
  validateEmail();
  validatePassword();
  
  return !validationErrors.name && !validationErrors.username && 
         !validationErrors.email && !validationErrors.password;
};

// Form validation
const isFormValid = computed(() => {
  return name.value.trim() !== '' && 
         username.value.trim() !== '' && 
         email.value.trim() !== '' && 
         password.value.trim().length >= 6 &&
         !validationErrors.name && 
         !validationErrors.username && 
         !validationErrors.email && 
         !validationErrors.password;
});

// Password strength
const passwordStrength = computed(() => {
  if (!password.value) return 0;
  
  let strength = 0;
  
  // Length check
  if (password.value.length >= 8) strength += 25;
  else if (password.value.length >= 6) strength += 15;
  
  // Complexity checks
  if (/[A-Z]/.test(password.value)) strength += 25;
  if (/[0-9]/.test(password.value)) strength += 25;
  if (/[^A-Za-z0-9]/.test(password.value)) strength += 25;
  
  return Math.min(100, strength);
});

const passwordStrengthClass = computed(() => {
  const strength = passwordStrength.value;
  if (strength >= 80) return 'strong';
  if (strength >= 50) return 'medium';
  return 'weak';
});

const passwordStrengthText = computed(() => {
  const strength = passwordStrength.value;
  if (strength >= 80) return 'Strong';
  if (strength >= 50) return 'Medium';
  return 'Weak';
});

const handleRegister = async () => {
  error.value = '';
  
  // Validate all fields before submission
  if (!validateAll()) {
    toastService.error('Please fix the errors in the form');
    return;
  }
  
  isLoading.value = true;
  
  try {
    await authService.register({
      name: name.value,
      username: username.value,
      email: email.value,
      password: password.value
    });
    
    // Show success message
    toastService.success('Account created successfully! Please login to continue.');
    
    // Redirect to login page
    router.push('/login');
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to create account. Please try again.';
    toastService.error(error.value);
  } finally {
    isLoading.value = false;
  }
};
</script> 