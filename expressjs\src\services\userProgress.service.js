import BaseService from './base.service.js';
import { userProgressRepository } from '../repositories/index.js';

class UserProgressService extends BaseService {
  constructor() {
    super(userProgressRepository);
  }

  /**
   * Get all progress for a specific user
   * @param {number} userId - User ID
   * @returns {Promise<Array>} - Array of progress records
   */
  async getUserProgress(userId) {
    return this.repository.findByUserId(userId);
  }

  /**
   * Submit exercise answer and update progress
   * @param {number} userId - User ID
   * @param {number} exerciseId - Exercise ID
   * @param {Object} answerData - User's answer data
   * @param {number} score - Score for the exercise
   * @returns {Promise<Object>} - Updated user progress
   */
  async submitExerciseAnswer(userId, exerciseId, answerData, score) {
    const [userProgress, created] = await this.repository.findOrCreateExerciseProgress(
      userId,
      exerciseId,
      {
        status: score >= 70 ? 'completed' : 'in_progress',
        score,
        answers: JSON.stringify(answerData),
        lastAccessedAt: new Date(),
        completedAt: score >= 70 ? new Date() : null
      }
    );
    
    if (!created) {
      userProgress.status = score >= 70 ? 'completed' : 'in_progress';
      userProgress.score = score;
      userProgress.answers = JSON.stringify(answerData);
      userProgress.lastAccessedAt = new Date();
      userProgress.completedAt = score >= 70 ? new Date() : userProgress.completedAt;
      await userProgress.save();
    }
    
    return userProgress;
  }
}

export default new UserProgressService(); 