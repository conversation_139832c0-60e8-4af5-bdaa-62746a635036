<script setup>
import { ref, reactive, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../stores/auth';
import { useThemeStore } from '../stores/theme';
import ThemeToggle from '../components/ThemeToggle.vue';

const router = useRouter();
const authStore = useAuthStore();
const themeStore = useThemeStore();
const isLoading = ref(false);
const errorMessage = ref('');
const isDark = computed(() => themeStore.isDark);

const form = reactive({
  email: '',
  password: ''
});

const login = async () => {
  try {
    isLoading.value = true;
    errorMessage.value = '';
    
    // Validate form
    if (!form.email || !form.password) {
      errorMessage.value = 'Email and password are required';
      return;
    }
    
    // Login using auth store
    const success = await authStore.login(form.email, form.password);
    
    if (success) {
      // Redirect to dashboard
      router.push('/');
    } else {
      errorMessage.value = 'Login failed. Please check your credentials.';
    }
  } catch (error) {
    console.error('Login error:', error);
    errorMessage.value = 'An error occurred during login. Please try again.';
  } finally {
    isLoading.value = false;
  }
};
</script>

<template>
  <div class="login-container" :class="{ 'dark': isDark }">
    <div class="login-card">
      <!-- Header -->
      <div class="login-header">
        <h1>Admin Panel</h1>
        <p>Sign in to your account</p>
      </div>
      
      <!-- Form -->
      <div class="login-form">
        <div class="theme-toggle-container">
          <ThemeToggle />
        </div>
        
        <div v-if="errorMessage" class="error-message">
          {{ errorMessage }}
        </div>
        
        <form @submit.prevent="login">
          <div class="form-group">
            <label for="email">Email address</label>
            <input
              id="email"
              type="email"
              v-model="form.email"
              required
              placeholder="<EMAIL>"
            />
          </div>
          
          <div class="form-group">
            <label for="password">Password</label>
            <input
              id="password"
              type="password"
              v-model="form.password"
              required
              placeholder="••••••••"
            />
          </div>
          
          <div class="form-options">
            <div class="remember-me">
              <input type="checkbox" id="remember-me" />
              <label for="remember-me">Remember me</label>
            </div>
            <a href="#" class="forgot-password">Forgot password?</a>
          </div>
          
          <button 
            type="submit" 
            :disabled="isLoading" 
            class="login-button"
          >
            {{ isLoading ? 'Signing in...' : 'Sign in' }}
          </button>
        </form>
      </div>
    </div>
  </div>
</template>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f3f4f6;
  padding: 1rem;
  transition: background-color 0.3s, color 0.3s;
}

.login-container.dark {
  background-color: #000;
  color: #f9fafb;
}

.login-card {
  width: 100%;
  max-width: 400px;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: background-color 0.3s, box-shadow 0.3s;
}

.login-container.dark .login-card {
  background-color: #111827;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.4);
}

.login-header {
  background-color: #6d28d9;
  color: white;
  text-align: center;
  padding: 1.5rem;
}

.login-header h1 {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.25rem;
}

.login-header p {
  font-size: 0.875rem;
  opacity: 0.8;
}

.login-form {
  padding: 1.5rem;
  position: relative;
}

.theme-toggle-container {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
}

.error-message {
  background-color: #fee2e2;
  border-left: 4px solid #ef4444;
  color: #b91c1c;
  padding: 0.75rem;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  border-radius: 4px;
}

.login-container.dark .error-message {
  background-color: rgba(239, 68, 68, 0.2);
  color: #fca5a5;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

.login-container.dark .form-group label {
  color: #d1d5db;
}

.form-group input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
  transition: border-color 0.3s, background-color 0.3s, color 0.3s;
}

.login-container.dark .form-group input {
  background-color: #1f2937;
  border-color: #374151;
  color: #f9fafb;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.remember-me {
  display: flex;
  align-items: center;
}

.remember-me input {
  margin-right: 0.5rem;
}

.remember-me label {
  font-size: 0.875rem;
  color: #374151;
}

.login-container.dark .remember-me label {
  color: #d1d5db;
}

.forgot-password {
  font-size: 0.875rem;
  color: #6d28d9;
  text-decoration: none;
}

.login-container.dark .forgot-password {
  color: #a78bfa;
}

.login-button {
  width: 100%;
  background-color: #6d28d9;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.login-button:hover {
  background-color: #5b21b6;
}

.login-container.dark .login-button {
  background-color: #6d28d9;
}

.login-container.dark .login-button:hover {
  background-color: #7c3aed;
}

.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}
</style> 