import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authService } from '../services/auth.service'

interface User {
  id: string;
  name?: string;
  email: string;
  username?: string;
  roles?: string[];
}

interface LoginCredentials {
  email: string;
  password: string;
}

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const isAuthenticated = ref(!!localStorage.getItem('accessToken'))

  // Check if user is authenticated
  const checkAuth = () => {
    const token = localStorage.getItem('accessToken')
    console.log('Checking auth, token exists:', !!token)
    isAuthenticated.value = !!token
    return isAuthenticated.value
  }

  // Login user
  const login = async (credentials: LoginCredentials) => {
    try {
      const response = await authService.login(credentials)
      console.log('Auth store login response:', response)
      
      // <PERSON><PERSON>m tra cấu trúc dữ liệu
      let userData, accessToken, refreshToken
      
      if (response.status === 'success' && response.data) {
        // Cấu trúc từ BaseController
        console.log('Using BaseController response structure')
        userData = response.data.user
        accessToken = response.data.accessToken
        refreshToken = response.data.refreshToken
      } else {
        // Cấu trúc trực tiếp
        console.log('Using direct response structure')
        userData = response.user
        accessToken = response.accessToken
        refreshToken = response.refreshToken
      }
      
      console.log('Extracted data:', { 
        user: userData ? 'exists' : 'missing', 
        accessToken: accessToken ? 'exists' : 'missing', 
        refreshToken: refreshToken ? 'exists' : 'missing' 
      })
      
      if (!accessToken) {
        console.error('No access token in response:', response)
        throw new Error('No access token received from server')
      }
      
      localStorage.setItem('accessToken', accessToken)
      if (refreshToken) {
        localStorage.setItem('refreshToken', refreshToken)
      }
      
      user.value = userData
      isAuthenticated.value = true
      return { userData, accessToken, refreshToken }
    } catch (error) {
      console.error('Login store error:', error)
      throw error
    }
  }

  // Logout user
  const logout = async () => {
    try {
      await authService.logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      localStorage.removeItem('accessToken')
      localStorage.removeItem('refreshToken')
      user.value = null
      isAuthenticated.value = false
    }
  }

  // Get current user
  const fetchCurrentUser = async () => {
    if (!checkAuth()) {
      console.log('Not authenticated, skipping profile fetch')
      return null
    }
    
    try {
      const response = await authService.getProfile()
      console.log('Profile data received:', response)
      
      // Kiểm tra cấu trúc dữ liệu
      let userData
      
      if (response.status === 'success' && response.data) {
        // Cấu trúc từ BaseController
        console.log('Using BaseController profile structure')
        if (response.data.user) {
          userData = response.data.user
        } else {
          userData = response.data
        }
      } else if (response.user) {
        // Cấu trúc có user
        console.log('Using user property in profile')
        userData = response.user
      } else {
        // Cấu trúc trực tiếp
        console.log('Using direct profile structure')
        userData = response
      }
      
      console.log('Final user data:', userData)
      user.value = userData
      return userData
    } catch (error) {
      console.error('Failed to fetch user profile:', error)
      return null
    }
  }

  return {
    user,
    isAuthenticated,
    checkAuth,
    login,
    logout,
    fetchCurrentUser
  }
}) 