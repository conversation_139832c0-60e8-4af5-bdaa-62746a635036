<template>
  <button 
    @click="themeStore.toggleTheme" 
    class="w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 shadow-sm hover:shadow"
    :title="themeStore.isDarkMode ? '<PERSON><PERSON><PERSON><PERSON> sang chế độ sáng' : '<PERSON><PERSON><PERSON><PERSON> sang chế độ tối'"
  >
    <!-- Icon mặt trời cho chế độ tối -->
    <font-awesome-icon v-if="themeStore.isDarkMode" icon="sun" class="h-5 w-5 text-yellow-400" />
    <!-- Icon mặt trăng cho chế độ sáng -->
    <font-awesome-icon v-else icon="moon" class="h-5 w-5 text-gray-700 dark:text-gray-300" />
  </button>
</template>

<script setup lang="ts">
import { useThemeStore } from '../stores/theme';

const themeStore = useThemeStore();

// Thêm export default để khắc phục lỗi
defineOptions({
  name: 'ThemeToggle'
});
</script> 