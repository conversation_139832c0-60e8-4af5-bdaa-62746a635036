<template>
  <div class="max-w-3xl mx-auto px-4 py-8">
    <div v-if="isLoading" class="text-center py-8 text-gray-600 dark:text-gray-400">
      Loading lesson...
    </div>
    
    <div v-else-if="error" class="text-center p-4 text-red-600 dark:text-red-400">
      {{ error }}
      <button @click="fetchLesson" class="mt-4 px-4 py-2 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">Retry</button>
    </div>
    
    <div v-else-if="lesson">
      <div class="flex justify-between items-center mb-8">
        <router-link :to="`/topics/${lesson.topicId}/lessons`" class="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
          &larr; Back to Lessons
        </router-link>
        
        <button 
          v-if="isAuthenticated" 
          @click="markLessonCompleted" 
          class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
          :disabled="isCompletingLesson"
        >
          {{ isCompletingLesson ? 'Marking as completed...' : 'Mark as Completed' }}
        </button>
      </div>
      
      <h1 class="text-3xl font-bold mb-4 dark:text-gray-100">{{ lesson.title }}</h1>
      
      <div class="my-8 leading-relaxed text-lg prose prose-lg dark:prose-invert max-w-none" v-html="formattedContent"></div>
      
      <div class="mt-12 text-center">
        <router-link :to="`/lessons/${lesson.id}/exercises`" class="px-6 py-3 bg-blue-500 text-white font-bold rounded-md hover:bg-blue-600 transition-colors">
          Practice Exercises
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { grammarService } from '../../services/grammar.service';
import { authService } from '../../services/auth.service';
import { marked } from 'marked';

const route = useRoute();
const router = useRouter();

interface Lesson {
  id: string;
  title: string;
  content: string;
  topicId: string;
  order: number;
}

const lesson = ref<Lesson | null>(null);
const isLoading = ref(true);
const error = ref('');
const isCompletingLesson = ref(false);

const lessonId = computed(() => {
  return route.params.id as string;
});

const isAuthenticated = computed(() => {
  return authService.isAuthenticated();
});

const formattedContent = computed(() => {
  if (!lesson.value?.content) return '';
  return marked(lesson.value.content);
});

const fetchLesson = async () => {
  error.value = '';
  isLoading.value = true;
  
  try {
    const response = await grammarService.getLessonWithExercises(lessonId.value);
    lesson.value = response;
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to load lesson.';
  } finally {
    isLoading.value = false;
  }
};

const markLessonCompleted = async () => {
  if (!isAuthenticated.value || !lesson.value) return;
  
  isCompletingLesson.value = true;
  
  try {
    await grammarService.markLessonCompleted(lesson.value.id);
    // Show success message or update UI
  } catch (err: any) {
    // Handle error
    if (err.response?.status === 401) {
      // Redirect to login if unauthorized
      router.push('/login');
    }
  } finally {
    isCompletingLesson.value = false;
  }
};

onMounted(fetchLesson);
</script>

<style>
/* Để tương thích với CSS đã được tạo ra bởi marked.js, chúng ta giữ lại một số style cơ bản */
.prose h2 {
  @apply text-2xl font-bold mt-8 mb-4;
}

.prose p {
  @apply mb-6;
}

.prose ul, .prose ol {
  @apply mb-6 pl-8;
}

.prose li {
  @apply mb-2;
}

/* Dark mode styles for prose content */
.dark .prose {
  @apply text-gray-300;
}

.dark .prose h1, .dark .prose h2, .dark .prose h3, .dark .prose h4, .dark .prose h5, .dark .prose h6 {
  @apply text-gray-100;
}

.dark .prose a {
  @apply text-blue-400;
}

.dark .prose strong {
  @apply text-gray-100;
}

.dark .prose blockquote {
  @apply border-gray-700 text-gray-400;
}

.dark .prose code {
  @apply bg-gray-800 text-gray-300;
}

.dark .prose pre {
  @apply bg-gray-800;
}
</style> 