import { userProgressService } from '../services/index.js';
import BaseController from './base.controller.js';

class UserProgressController extends BaseController {
  constructor() {
    super();
  }

  // Get all progress for the authenticated user
  async getUserProgress(req, res) {
    try {
      // Kiểm tra xem req.user có tồn tại không
      if (!req.user || !req.user.id) {
        return res.status(401).json({
          success: false,
          message: 'Unauthorized: User not authenticated or missing ID'
        });
      }
      
      const userId = req.user.id;
      console.log(`Fetching progress for user ID: ${userId}`);
      
      const progress = await userProgressService.getUserProgress(userId);
      
      return res.status(200).json({
        success: true,
        data: progress
      });
    } catch (error) {
      console.error('Error in getUserProgress:', error);
      return res.status(500).json({
        success: false,
        message: 'Error retrieving user progress',
        error: error.message
      });
    }
  }

  // Submit exercise answer
  async submitExerciseAnswer(req, res) {
    try {
      // Kiểm tra xem req.user có tồn tại không
      if (!req.user || !req.user.id) {
        return res.status(401).json({
          success: false,
          message: 'Unauthorized: User not authenticated or missing ID'
        });
      }
      
      const { exerciseId } = req.params;
      const { answers, score } = req.body;
      const userId = req.user.id;
      
      if (!exerciseId) {
        return res.status(400).json({
          success: false,
          message: 'Exercise ID is required'
        });
      }
      
      if (!answers || score === undefined) {
        return res.status(400).json({
          success: false,
          message: 'Answers and score are required'
        });
      }
      
      const userProgress = await userProgressService.submitExerciseAnswer(
        userId,
        exerciseId,
        answers,
        score
      );
      
      return res.status(200).json({
        success: true,
        message: 'Exercise answer submitted successfully',
        data: userProgress
      });
    } catch (error) {
      console.error('Error in submitExerciseAnswer:', error);
      return res.status(500).json({
        success: false,
        message: 'Error submitting exercise answer',
        error: error.message
      });
    }
  }
}

export default new UserProgressController(); 