import api from './api';

interface GrammarTopic {
  id: string;
  title: string;
  description: string;
  level: string;
  imageUrl?: string;
}

interface Lesson {
  id: string;
  title: string;
  content: string;
  topicId: string;
  order: number;
}

interface Exercise {
  id: string;
  question: string;
  options?: string[];
  correctAnswer: string;
  explanation: string;
  lessonId: string;
  type: string;
  difficulty: string;
}

interface UserProgress {
  userId: string;
  completedLessons?: string[];
  exerciseResults?: {
    exerciseId: string;
    correct: boolean;
    attemptCount: number;
  }[];
}

interface ApiResponse<T> {
  success?: boolean;
  data?: T;
  status?: string;
  message?: string;
}

interface AnswerSubmission {
  answer: string;
}

// Interface cho dữ liệu exercises từ API
interface ApiExercise {
  id: number;
  lessonId: number;
  title: string;
  type: string;
  instructions: string;
  content: string;
  answers: string;
  difficulty: string;
  points: number;
  order: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export const grammarService = {
  // Public routes
  async getAllTopics(): Promise<GrammarTopic[]> {
    const response = await api.get('/grammar/topics');
    return response.data.data;
  },

  async getTopicsByLevel(level: string): Promise<GrammarTopic[]> {
    const response = await api.get(`/grammar/topics/level/${level}`);
    return response.data.data;
  },

  async getTopicWithLessons(topicId: string): Promise<GrammarTopic & { lessons: Lesson[] }> {
    const response = await api.get(`/grammar/topics/${topicId}`);
    return response.data.data;
  },

  async getLessonsByTopic(topicId: string): Promise<Lesson[]> {
    const response = await api.get(`/grammar/lessons/topic/${topicId}`);
    return response.data.data;
  },

  async getLessonWithExercises(lessonId: string): Promise<Lesson & { exercises: Exercise[] }> {
    const response = await api.get(`/grammar/lessons/${lessonId}`);
    return response.data.data;
  },

  async getExercisesByLesson(lessonId: string): Promise<Exercise[]> {
    try {
      console.log('Fetching exercises for lesson:', lessonId);
      const response = await api.get(`/grammar/exercises/lesson/${lessonId}`);
      console.log('Raw API response:', response.data);
      
      if (!response.data.success || !response.data.data || !Array.isArray(response.data.data)) {
        console.error('Invalid API response format:', response.data);
        return [];
      }
      
      // Chuyển đổi dữ liệu từ API sang định dạng mà component cần
      const transformedExercises = response.data.data.map((apiExercise: ApiExercise) => {
        let question = '';
        let options: string[] = [];
        let correctAnswer = '';
        let explanation = 'Great job!';
        
        try {
          // Xử lý dữ liệu dựa trên loại bài tập
          if (apiExercise.type === 'multiple-choice') {
            // Parse content (chứa câu hỏi và các lựa chọn)
            const contentArray = JSON.parse(apiExercise.content);
            // Lấy câu hỏi đầu tiên làm ví dụ (có thể điều chỉnh sau)
            const firstQuestion = contentArray[0];
            question = firstQuestion.question;
            options = firstQuestion.options;
            
            // Parse answers để lấy đáp án đúng
            const answersArray = JSON.parse(apiExercise.answers);
            correctAnswer = answersArray[0];
          } else if (apiExercise.type === 'fill-in-blanks') {
            // Parse content
            const contentObj = JSON.parse(apiExercise.content);
            question = contentObj.text || apiExercise.instructions;
            
            // Parse answers
            const answersArray = JSON.parse(apiExercise.answers);
            correctAnswer = answersArray[0];
          } else {
            // Cho các loại bài tập khác, sử dụng instructions làm câu hỏi
            question = apiExercise.instructions;
          }
        } catch (error) {
          console.error('Error parsing exercise data:', error);
          question = apiExercise.title;
        }
        
        return {
          id: apiExercise.id.toString(),
          question: question,
          options: options,
          correctAnswer: correctAnswer,
          explanation: explanation,
          lessonId: apiExercise.lessonId.toString(),
          type: apiExercise.type,
          difficulty: apiExercise.difficulty
        };
      });
      
      console.log('Transformed exercises:', transformedExercises);
      return transformedExercises;
    } catch (error) {
      console.error('Error in getExercisesByLesson:', error);
      throw error;
    }
  },

  async getExercisesByDifficulty(difficulty: string): Promise<Exercise[]> {
    const response = await api.get(`/grammar/exercises/difficulty/${difficulty}`);
    return response.data.data;
  },

  // Protected routes (require authentication)
  async markLessonCompleted(lessonId: string): Promise<void> {
    await api.post(`/grammar/lessons/${lessonId}/complete`);
  },

  async getUserProgress(): Promise<any> {
    try {
      const response = await api.get('/grammar/progress');
      console.log('Raw progress response:', response);
      
      // Trả về toàn bộ response.data để xử lý ở component
      return response.data;
    } catch (error) {
      console.error('Error in getUserProgress service:', error);
      throw error;
    }
  },

  async submitExerciseAnswer(exerciseId: string, submission: AnswerSubmission): Promise<{ correct: boolean; explanation: string }> {
    try {
      console.log('Submitting answer for exercise:', exerciseId, 'with submission:', submission);
      
      // API yêu cầu answers và score
      const response = await api.post(`/grammar/exercises/${exerciseId}/submit`, {
        answers: submission.answer,
        score: 100 // Giả định điểm số, có thể điều chỉnh nếu cần
      });
      
      console.log('Submit answer response:', response.data);
      
      return {
        correct: true,
        explanation: 'Answer submitted successfully'
      };
    } catch (error) {
      console.error('Error submitting exercise answer:', error);
      // Không throw lỗi vì đây là chức năng không quan trọng
      return {
        correct: false,
        explanation: 'Failed to submit answer'
      };
    }
  },

  async validateAnswer(exerciseId: string, submission: AnswerSubmission): Promise<{ isCorrect: boolean; feedback: string }> {
    try {
      console.log('Validating answer for exercise:', exerciseId, 'with submission:', submission);
      
      // API yêu cầu dữ liệu trong định dạng { answers: answer } chứ không phải { answer: answer }
      const response = await api.post(`/grammar/exercises/${exerciseId}/validate`, {
        answers: submission.answer
      });
      
      console.log('Validate answer response:', response.data);
      
      // Chuyển đổi định dạng phản hồi từ API sang định dạng mà component cần
      const result = response.data;
      
      return {
        isCorrect: result.data?.passed || false,
        feedback: result.data?.feedback || 'No feedback available'
      };
    } catch (error) {
      console.error('Error validating answer:', error);
      throw error;
    }
  }
}; 