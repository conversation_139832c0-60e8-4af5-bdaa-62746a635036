<template>
  <div class="container mx-auto px-4 py-8">
    <div class="max-w-3xl mx-auto">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-800 dark:text-gray-100">Your Profile</h1>
        <p class="mt-2 text-gray-600 dark:text-gray-400">Manage your account information and preferences</p>
      </div>
      
      <!-- Loading state -->
      <div v-if="isLoading" class="flex justify-center items-center py-16">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
      
      <!-- Error state -->
      <div v-else-if="error" class="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg p-6 text-center">
        <div class="text-red-600 dark:text-red-400 text-lg mb-4">{{ error }}</div>
        <button 
          @click="fetchProfile" 
          class="px-4 py-2 bg-red-100 dark:bg-red-800 text-red-600 dark:text-red-300 rounded-md hover:bg-red-200 dark:hover:bg-red-700 transition-colors"
        >
          Retry
        </button>
      </div>
      
      <!-- Profile content -->
      <div v-else-if="profile" class="bg-white dark:bg-dark-card rounded-xl shadow-md overflow-hidden">
        <!-- Profile header -->
        <div class="p-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white">
          <div class="flex items-center">
            <div class="h-20 w-20 rounded-full bg-white/20 flex items-center justify-center text-3xl font-bold">
              {{ getInitials(profile.name || profile.username || profile.email) }}
            </div>
            <div class="ml-6">
              <h2 class="text-2xl font-bold">{{ profile.name || profile.username }}</h2>
              <div class="flex items-center mt-1 text-blue-100">
                <span class="bg-blue-500/30 px-2 py-0.5 rounded-full text-sm">
                  {{ profile.roles ? profile.roles.join(', ') : 'User' }}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Profile details -->
        <div class="p-6 space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Email -->
            <div class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
              <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Email</div>
              <div class="text-gray-800 dark:text-gray-200 font-medium">{{ profile.email }}</div>
            </div>
            
            <!-- Username -->
            <div class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
              <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Username</div>
              <div class="text-gray-800 dark:text-gray-200 font-medium">{{ profile.username || 'Not set' }}</div>
            </div>
            
            <!-- Account status -->
            <div class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
              <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Account Status</div>
              <div class="flex items-center">
                <span 
                  class="inline-block w-2 h-2 rounded-full mr-2"
                  :class="profile.isActive ? 'bg-green-500' : 'bg-red-500'"
                ></span>
                <span class="text-gray-800 dark:text-gray-200 font-medium">
                  {{ profile.isActive ? 'Active' : 'Inactive' }}
                </span>
              </div>
            </div>
            
            <!-- Created at -->
            <div class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
              <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Member Since</div>
              <div class="text-gray-800 dark:text-gray-200 font-medium">
                {{ formatDate(profile.createdAt) }}
              </div>
            </div>
          </div>
        </div>
        
        <!-- Actions -->
        <div class="p-6 bg-gray-50 dark:bg-gray-800/30 border-t border-gray-100 dark:border-gray-700/50 flex flex-wrap gap-4">
          <router-link 
            to="/progress" 
            class="px-6 py-3 bg-primary text-white rounded-lg flex items-center justify-center hover:bg-primary-dark transition-colors"
          >
            <span class="mr-2">📊</span>
            View Learning Progress
          </router-link>
          
          <button 
            @click="handleLogout" 
            class="px-6 py-3 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center"
          >
            <span class="mr-2">🚪</span>
            Logout
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { authService } from '../../services/auth.service';
import { useAuthStore } from '../../stores/auth';

const router = useRouter();
const authStore = useAuthStore();

interface UserProfile {
  id: string;
  name?: string;
  email: string;
  username?: string;
  roles?: string[];
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

const profile = ref<UserProfile | null>(null);
const isLoading = ref(true);
const error = ref('');

// Format date to readable format
const formatDate = (dateString?: string): string => {
  if (!dateString) return 'N/A';
  
  try {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    }).format(date);
  } catch (e) {
    return dateString;
  }
};

// Get initials from name
const getInitials = (name: string): string => {
  if (!name) return '?';
  
  return name
    .split(' ')
    .map(word => word[0])
    .join('')
    .toUpperCase()
    .substring(0, 2);
};

const fetchProfile = async () => {
  error.value = '';
  isLoading.value = true;
  
  try {
    console.log('Fetching profile data...');
    const response = await authService.getProfile();
    console.log('Profile response:', response);
    
    if (response.status === 'success' && response.data && response.data.user) {
      profile.value = response.data.user;
    } else if (response.status === 'success' && response.data) {
      profile.value = response.data;
    } else if (response.user) {
      profile.value = response.user;
    } else {
      profile.value = response;
    }
    
    console.log('Final profile data:', profile.value);
  } catch (err: any) {
    console.error('Profile fetch error:', err);
    error.value = err.response?.data?.message || 'Failed to load profile information.';
  } finally {
    isLoading.value = false;
  }
};

const handleLogout = async () => {
  try {
    await authStore.logout();
    router.push('/login');
  } catch (err) {
    console.error('Logout error:', err);
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    router.push('/login');
  }
};

onMounted(fetchProfile);
</script> 