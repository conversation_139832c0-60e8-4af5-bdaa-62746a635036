import { lessonService } from '../services/index.js';
import BaseController from './base.controller.js';

class LessonController extends BaseController {
  constructor() {
    super();
  }

  // Get lesson with its exercises
  async getLessonWithExercises(req, res) {
    try {
      const { id } = req.params;
      const lesson = await lessonService.getLessonWithExercises(id);
      
      return res.status(200).json({
        success: true,
        data: lesson
      });
    } catch (error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: error.message
        });
      }
      
      return res.status(500).json({
        success: false,
        message: 'Error retrieving lesson with exercises',
        error: error.message
      });
    }
  }

  // Get lessons by topic ID
  async getLessonsByTopic(req, res) {
    try {
      const { topicId } = req.params;
      const lessons = await lessonService.getLessonsByTopic(topicId);
      
      return res.status(200).json({
        success: true,
        data: lessons
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: 'Error retrieving lessons by topic',
        error: error.message
      });
    }
  }

  // <PERSON> lesson as completed for user
  async markLessonCompleted(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id; // Assuming user is authenticated
      
      const userProgress = await lessonService.markLessonCompleted(id, userId);
      
      return res.status(200).json({
        success: true,
        message: 'Lesson marked as completed',
        data: userProgress
      });
    } catch (error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: error.message
        });
      }
      
      return res.status(500).json({
        success: false,
        message: 'Error marking lesson as completed',
        error: error.message
      });
    }
  }
}

export default new LessonController(); 