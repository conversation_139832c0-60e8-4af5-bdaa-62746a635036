<script setup>
import { computed } from 'vue'

const currentYear = computed(() => new Date().getFullYear())
</script>

<template>
  <footer class="footer">
    <div class="footer-container">
      <div class="footer-content">
        <div class="copyright">
          &copy; {{ currentYear }} Admin Dashboard. All rights reserved.
        </div>
        
        <div class="footer-links">
          <a href="#" class="footer-link">Privacy Policy</a>
          <a href="#" class="footer-link">Terms of Service</a>
          <a href="#" class="footer-link">Contact</a>
        </div>
      </div>
      
      <div class="version-info">
        <span>v1.0.0</span>
      </div>
    </div>
  </footer>
</template>

<style scoped>
.footer {
  background-color: #1f2937;
  color: #d1d5db;
  padding: 1.5rem 0;
  margin-top: auto;
  font-size: 0.875rem;
}

.footer-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 1rem;
}

.footer-links {
  display: flex;
  gap: 1.5rem;
}

.footer-link {
  color: #d1d5db;
  text-decoration: none;
  transition: color 0.2s;
}

.footer-link:hover {
  color: white;
  text-decoration: underline;
}

.version-info {
  text-align: right;
  color: #9ca3af;
  font-size: 0.75rem;
  border-top: 1px solid #374151;
  padding-top: 1rem;
}

/* Dark mode overrides */
:deep(.dark-mode) .footer {
  background-color: #000;
  border-top: 1px solid #374151;
}
</style> 