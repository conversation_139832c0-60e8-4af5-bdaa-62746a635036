import { DataTypes, Model } from 'sequelize';
import { sequelize } from '../config/database.js';

class UserAchievement extends Model {}

UserAchievement.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  achievementId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'achievements',
      key: 'id'
    }
  },
  earnedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  progress: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'For tracking partial progress towards an achievement'
  },
  createdAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updatedAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  sequelize,
  modelName: 'UserAchievement',
  tableName: 'user_achievements',
  timestamps: true,
  indexes: [
    {
      name: 'user_achievement_idx',
      fields: ['userId', 'achievementId'],
      unique: true
    }
  ]
});

export default UserAchievement; 