import api from './api';

interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterData {
  name: string;
  username: string;
  email: string;
  password: string;
}

// <PERSON><PERSON><PERSON> user
interface User {
  id: string;
  name?: string;
  username?: string;
  email: string;
  roles?: string[];
}

// Cấu trúc dữ liệu trả về trực tiếp
interface AuthResponseData {
  accessToken: string;
  refreshToken: string;
  user: User;
}

// Cấu trúc dữ liệu từ BaseController
interface AuthResponse {
  status: string;
  data: AuthResponseData;
}

export const authService = {
  async login(credentials: LoginCredentials): Promise<any> {
    try {
      const response = await api.post('/auth/login', credentials);
      console.log('Login API response:', response);
      console.log('Response data:', response.data);
      
      // Trả về toàn bộ response.data để xử lý ở store
      return response.data;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  },

  async register(userData: RegisterData): Promise<any> {
    const response = await api.post('/auth/register', userData);
    console.log('Register API response:', response.data);
    
    // Trả về toàn bộ response.data để xử lý ở store
    return response.data;
  },

  async logout(): Promise<void> {
    await api.post('/auth/logout');
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
  },

  async getProfile(): Promise<any> {
    const response = await api.get('/auth/profile');
    console.log('Profile API response:', response.data);
    
    // Trả về toàn bộ response.data để xử lý ở store
    return response.data;
  },

  isAuthenticated(): boolean {
    const token = localStorage.getItem('accessToken');
    console.log('Current accessToken in localStorage:', token);
    return !!token;
  }
}; 