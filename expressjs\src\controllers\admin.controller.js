import BaseController from './base.controller.js';
import { userService } from '../services/index.js';
import JwtUtils from '../utils/jwt.utils.js';
import bcrypt from 'bcryptjs';
import jwtConfig from '../config/jwt.config.js';

class AdminController extends BaseController {
  constructor() {
    super();
    // Bind methods to ensure 'this' refers to AdminController instance
    this.login = this.catchAsync(this.login.bind(this));
    this.logout = this.catchAsync(this.logout.bind(this));
    this.getProfile = this.catchAsync(this.getProfile.bind(this));
  }

  /**
   * Admin login
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async login(req, res) {
    console.log('Admin login request body:', req.body);
    
    // Ensure req.body is not undefined or empty
    if (!req.body || Object.keys(req.body).length === 0) {
      return this.sendError(res, 'Invalid request format', 400);
    }
    
    const { email, password } = req.body;
    
    // Check if email and password are provided
    if (!email || !password) {
      return this.sendError(res, 'Email and password are required', 400);
    }
    
    try {
      // Find user by email
      const user = await userService.findByEmail(email);
      
      if (!user) {
        return this.sendError(res, 'Invalid credentials', 401);
      }
      
      // Check if user has admin role
      const userRoles = user.roles || ['user'];
      if (!userRoles.includes('admin')) {
        return this.sendError(res, 'Unauthorized: Admin access required', 403);
      }
      
      // Check password
      const isPasswordValid = await bcrypt.compare(password, user.password);
      
      if (!isPasswordValid) {
        return this.sendError(res, 'Invalid credentials', 401);
      }
      
      console.log('Admin login successful for:', email);
      
      // Generate tokens with admin role
      const accessToken = JwtUtils.generateAccessToken({
        id: user.id,
        email: user.email,
        roles: userRoles
      });
      
      const refreshToken = JwtUtils.generateRefreshToken({
        id: user.id,
        email: user.email,
        roles: userRoles
      });
      
      // Set cookies
      res.cookie('adminAccessToken', accessToken, jwtConfig.cookieOptions);
      res.cookie('adminRefreshToken', refreshToken, {
        ...jwtConfig.cookieOptions,
        maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
      });
      
      // Return user data without password
      const { password: _, ...userWithoutPassword } = user.toJSON();
      
      this.sendSuccess(res, {
        user: userWithoutPassword,
        accessToken,
        refreshToken
      });
    } catch (error) {
      console.error('Admin login error:', error);
      this.sendError(res, error.message, 500);
    }
  }

  /**
   * Admin logout
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async logout(req, res) {
    // Clear cookies
    res.clearCookie('adminAccessToken');
    res.clearCookie('adminRefreshToken');
    
    this.sendSuccess(res, {
      message: 'Admin logged out successfully'
    });
  }

  /**
   * Get admin profile
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getProfile(req, res) {
    try {
      const user = await userService.getById(req.user.id);
      
      if (!user) {
        return this.sendError(res, 'User not found', 404);
      }
      
      // Check if user has admin role
      const userRoles = user.roles || ['user'];
      if (!userRoles.includes('admin')) {
        return this.sendError(res, 'Unauthorized: Admin access required', 403);
      }
      
      const { password, ...userWithoutPassword } = user.toJSON();
      
      this.sendSuccess(res, {
        user: userWithoutPassword
      });
    } catch (error) {
      this.sendError(res, error.message, 500);
    }
  }
}

const adminController = new AdminController();
export default adminController; 