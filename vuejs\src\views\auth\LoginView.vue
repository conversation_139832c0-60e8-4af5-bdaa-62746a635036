<template>
  <div class="flex justify-center items-center min-h-[calc(100vh-200px)] py-8 px-4">
    <div class="w-full max-w-lg bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden">
      <div class="p-8 bg-gradient-to-r from-blue-600 to-blue-700 text-white text-center">
        <h1 class="text-2xl font-bold mb-2">Welcome Back</h1>
        <p class="opacity-80">Sign in to continue your learning journey</p>
      </div>
      
      <div v-if="redirectMessage" class="mx-8 mt-6 p-4 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-lg flex items-center">
        <span class="mr-2">ℹ️</span>
        {{ redirectMessage }}
      </div>
      
      <form @submit.prevent="handleLogin" class="p-8">
        <div class="mb-6">
          <label for="email" class="flex items-center mb-2 font-medium text-gray-700 dark:text-gray-300">
            <span class="mr-2 text-primary">✉️</span>
            Email
          </label>
          <input 
            type="email" 
            id="email" 
            v-model="email" 
            required 
            placeholder="Enter your email"
            autocomplete="email"
            class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
            :class="{'border-red-500 dark:border-red-400': validationErrors.email, 'border-gray-300 dark:border-gray-600': !validationErrors.email}"
            @blur="validateEmail"
          />
          <p v-if="validationErrors.email" class="mt-1 text-sm text-red-500 dark:text-red-400">{{ validationErrors.email }}</p>
        </div>
        
        <div class="mb-6 relative">
          <label for="password" class="flex items-center mb-2 font-medium text-gray-700 dark:text-gray-300">
            <span class="mr-2 text-primary">🔒</span>
            Password
          </label>
          <div class="relative">
            <input 
              :type="showPassword ? 'text' : 'password'" 
              id="password" 
              v-model="password" 
              required 
              placeholder="Enter your password"
              autocomplete="current-password"
              class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
              :class="{'border-red-500 dark:border-red-400': validationErrors.password, 'border-gray-300 dark:border-gray-600': !validationErrors.password}"
              @blur="validatePassword"
            />
            <button 
              type="button" 
              class="absolute right-3 top-1/2 -translate-y-1/2 bg-transparent border-none text-gray-500 dark:text-gray-400 cursor-pointer p-0"
              @click="showPassword = !showPassword"
            >
              <span>{{ showPassword ? '👁️‍🗨️' : '👁️' }}</span>
            </button>
          </div>
          <p v-if="validationErrors.password" class="mt-1 text-sm text-red-500 dark:text-red-400">{{ validationErrors.password }}</p>
          <div class="text-right mt-2 text-sm">
            <a href="#" @click.prevent="forgotPassword" class="text-primary hover:text-primary-dark">Forgot password?</a>
          </div>
        </div>
        
        <div v-if="error" class="bg-red-50 dark:bg-red-900/30 text-red-600 dark:text-red-400 p-3 rounded-lg mb-6 flex items-center">
          <span class="mr-2">⚠️</span>
          {{ error }}
        </div>
        
        <button 
          type="submit" 
          class="w-full py-3 bg-primary text-white font-bold rounded-lg transition-colors duration-300 hover:bg-primary-dark disabled:bg-gray-300 disabled:cursor-not-allowed flex justify-center items-center"
          :disabled="isLoading || !isFormValid"
        >
          <span v-if="isLoading" class="flex items-center">
            <span class="mr-2 animate-spin inline-block">⏳</span>
            Signing in...
          </span>
          <span v-else>Sign In</span>
        </button>
        
        <div class="text-center mt-6 text-gray-500 dark:text-gray-400">
          Don't have an account? 
          <router-link to="/register" class="text-primary font-medium hover:text-primary-dark">Create Account</router-link>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { toastService } from '../../services/toast.service';
import { useAuthStore } from '../../stores/auth';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();

const email = ref('');
const password = ref('');
const error = ref('');
const isLoading = ref(false);
const showPassword = ref(false);
const redirectMessage = ref('');

// Validation errors
const validationErrors = reactive({
  email: '',
  password: ''
});

// Validation functions
const validateEmail = () => {
  validationErrors.email = '';
  if (email.value.trim() === '') {
    validationErrors.email = 'Email is required';
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.value)) {
    validationErrors.email = 'Please enter a valid email address';
  }
};

const validatePassword = () => {
  validationErrors.password = '';
  if (password.value.trim() === '') {
    validationErrors.password = 'Password is required';
  }
};

const validateAll = () => {
  validateEmail();
  validatePassword();
  
  return !validationErrors.email && !validationErrors.password;
};

// Form validation
const isFormValid = computed(() => {
  return email.value.trim() !== '' && 
         password.value.trim() !== '' &&
         !validationErrors.email &&
         !validationErrors.password;
});

onMounted(() => {
  // Check if there's a redirect message
  if (route.query.message) {
    redirectMessage.value = route.query.message as string;
    toastService.info(redirectMessage.value);
  }
});

const handleLogin = async () => {
  error.value = '';
  
  // Validate all fields before submission
  if (!validateAll()) {
    toastService.error('Please fix the errors in the form');
    return;
  }
  
  isLoading.value = true;
  
  try {
    await authStore.login({
      email: email.value,
      password: password.value
    });
    
    // Show success message
    toastService.success('Logged in successfully!');
    
    // Redirect to intended page or home
    const redirectPath = route.query.redirect as string || '/';
    router.push(redirectPath);
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to login. Please check your credentials.';
    toastService.error(error.value);
  } finally {
    isLoading.value = false;
  }
};

const forgotPassword = () => {
  toastService.info('Password reset functionality is coming soon!');
};
</script> 