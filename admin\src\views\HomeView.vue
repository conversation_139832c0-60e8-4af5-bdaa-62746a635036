<script setup lang="ts">
import { ref } from 'vue';

const stats = ref([
  { label: 'Người dùng', value: '1,234', icon: 'pi pi-users', color: 'from-blue-500 to-blue-700', iconBg: 'bg-blue-600', trend: '+12%', trendUp: true },
  { label: 'Sản phẩm', value: '567', icon: 'pi pi-shopping-cart', color: 'from-emerald-500 to-green-700', iconBg: 'bg-emerald-600', trend: '+8%', trendUp: true },
  { label: 'Đơn hàng', value: '89', icon: 'pi pi-shopping-bag', color: 'from-amber-500 to-orange-700', iconBg: 'bg-amber-600', trend: '-3%', trendUp: false },
  { label: 'Doanh thu', value: '$12,345', icon: 'pi pi-dollar', color: 'from-purple-500 to-indigo-700', iconBg: 'bg-purple-600', trend: '+23%', trendUp: true }
]);

const chartData = ref({
  labels: ['Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6', 'Tháng 7'],
  datasets: [
    {
      label: 'Doanh số',
      data: [65, 59, 80, 81, 56, 55, 40],
      fill: true,
      backgroundColor: 'rgba(16, 185, 129, 0.2)',
      borderColor: '#10b981',
      tension: 0.4
    },
    {
      label: 'Doanh thu',
      data: [28, 48, 40, 19, 86, 27, 90],
      fill: true,
      backgroundColor: 'rgba(139, 92, 246, 0.2)',
      borderColor: '#8b5cf6',
      tension: 0.4
    }
  ]
});

const chartOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top',
      labels: {
        font: {
          size: 14
        }
      }
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      grid: {
        display: true,
        color: 'rgba(0, 0, 0, 0.05)'
      }
    },
    x: {
      grid: {
        color: 'rgba(0, 0, 0, 0.05)'
      }
    }
  }
});

const recentActivities = ref([
  { user: 'Nguyễn Văn A', avatar: 'https://randomuser.me/api/portraits/men/32.jpg', action: 'đã tạo sản phẩm mới', time: '5 phút trước', category: 'Sản phẩm', color: 'bg-blue-100 text-blue-800' },
  { user: 'Trần Thị B', avatar: 'https://randomuser.me/api/portraits/women/44.jpg', action: 'đã cập nhật kho hàng', time: '2 giờ trước', category: 'Kho hàng', color: 'bg-green-100 text-green-800' },
  { user: 'Lê Văn C', avatar: 'https://randomuser.me/api/portraits/men/59.jpg', action: 'đã xử lý đơn hàng', time: '4 giờ trước', category: 'Đơn hàng', color: 'bg-amber-100 text-amber-800' },
  { user: 'Phạm Thị D', avatar: 'https://randomuser.me/api/portraits/women/17.jpg', action: 'đã thêm danh mục mới', time: '1 ngày trước', category: 'Danh mục', color: 'bg-purple-100 text-purple-800' }
]);

const topProducts = ref([
  { name: 'Laptop Gaming XYZ', sales: 245, percentage: 35, color: 'bg-gradient-to-r from-blue-500 to-blue-700' },
  { name: 'Smartphone ABC', sales: 190, percentage: 27, color: 'bg-gradient-to-r from-emerald-500 to-green-700' },
  { name: 'Tai nghe không dây', sales: 120, percentage: 18, color: 'bg-gradient-to-r from-amber-500 to-orange-700' },
  { name: 'Bàn phím cơ RGB', sales: 85, percentage: 12, color: 'bg-gradient-to-r from-purple-500 to-indigo-700' },
  { name: 'Chuột gaming', sales: 55, percentage: 8, color: 'bg-gradient-to-r from-pink-500 to-rose-700' }
]);
</script>

<template>
  <div class="py-6 px-4 bg-gray-50 min-h-screen">
    <!-- Header Section -->
    <div class="mb-8 bg-gradient-to-r from-blue-600 to-indigo-700 rounded-xl p-6 shadow-lg text-white">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-3xl font-bold">Tổng quan hệ thống</h1>
          <p class="mt-2 text-blue-100">Chào mừng trở lại! Đây là thông tin tổng quan của bạn.</p>
        </div>
        <div>
          <Button label="Tải báo cáo" icon="pi pi-download" class="bg-white text-indigo-700 border-none hover:bg-blue-50" />
        </div>
      </div>
    </div>
    
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div v-for="(stat, index) in stats" :key="index" class="transform transition-all duration-300 hover:scale-105 hover:shadow-lg">
        <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
          <div class="bg-gradient-to-r p-1" :class="stat.color"></div>
          <div class="p-5">
            <div class="flex justify-between">
              <div>
                <p class="text-gray-500 text-sm mb-1">{{ stat.label }}</p>
                <h2 class="text-2xl font-bold text-gray-800">{{ stat.value }}</h2>
                <div class="flex items-center mt-2">
                  <span :class="[
                    'text-xs font-medium px-2 py-1 rounded-full', 
                    stat.trendUp ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  ]">
                    <i :class="[
                      'pi mr-1 text-xs', 
                      stat.trendUp ? 'pi-arrow-up' : 'pi-arrow-down'
                    ]"></i>
                    {{ stat.trend }}
                  </span>
                  <span class="text-xs text-gray-500 ml-2">vs. tháng trước</span>
                </div>
              </div>
              <div :class="['rounded-full w-12 h-12 flex items-center justify-center', stat.iconBg]">
                <i :class="[stat.icon, 'text-white text-xl']"></i>
              </div>
            </div>
            <div class="mt-4 w-full h-2 bg-gray-100 rounded-full overflow-hidden">
              <div :class="['h-full bg-gradient-to-r', stat.color]" style="width: 75%"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Charts & Activities -->
    <div class="grid grid-cols-1 lg:grid-cols-12 gap-6 mb-8">
      <div class="lg:col-span-8">
        <div class="bg-white rounded-xl shadow-md p-5 border border-gray-100 h-full">
          <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-bold text-gray-800 flex items-center">
              <i class="pi pi-chart-line text-blue-600 mr-2"></i>
              Phân tích doanh số
            </h2>
            <div class="flex gap-2">
              <Button icon="pi pi-calendar" class="p-button-text p-button-rounded" />
              <Button icon="pi pi-filter" class="p-button-text p-button-rounded" />
              <Button icon="pi pi-ellipsis-v" class="p-button-text p-button-rounded" />
            </div>
          </div>
          <div class="h-80">
            <Chart type="line" :data="chartData" :options="chartOptions" />
          </div>
        </div>
      </div>
      
      <div class="lg:col-span-4">
        <div class="bg-white rounded-xl shadow-md p-5 border border-gray-100 h-full">
          <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-bold text-gray-800 flex items-center">
              <i class="pi pi-bell text-purple-600 mr-2"></i>
              Hoạt động gần đây
            </h2>
            <Button icon="pi pi-ellipsis-h" class="p-button-text p-button-rounded" />
          </div>
          <div class="overflow-y-auto" style="max-height: 320px">
            <div v-for="(activity, index) in recentActivities" :key="index" 
                class="flex items-start mb-5 pb-5 transform transition-all duration-300 hover:bg-gray-50 p-2 rounded-lg" 
                :class="{'border-b border-gray-100': index !== recentActivities.length - 1}">
              <img :src="activity.avatar" class="w-10 h-10 rounded-full mr-3 object-cover ring-2 ring-offset-2" :class="activity.color.replace('bg-', 'ring-').replace('text-', '')" />
              <div>
                <div class="flex items-center flex-wrap">
                  <span class="font-medium text-gray-800">{{ activity.user }}</span>
                  <span :class="['text-xs px-2 py-1 rounded-full ml-2', activity.color]">{{ activity.category }}</span>
                </div>
                <p class="text-gray-600 mt-1">{{ activity.action }}</p>
                <span class="text-xs text-gray-400 flex items-center mt-1">
                  <i class="pi pi-clock mr-1"></i>
                  {{ activity.time }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Bottom Row -->
    <div class="grid grid-cols-1 lg:grid-cols-12 gap-6">
      <div class="lg:col-span-4">
        <div class="bg-white rounded-xl shadow-md p-5 border border-gray-100 h-full">
          <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-bold text-gray-800 flex items-center">
              <i class="pi pi-star text-amber-500 mr-2"></i>
              Top sản phẩm
            </h2>
            <Button label="Xem tất cả" class="p-button-text" />
          </div>
          <div class="space-y-4">
            <div v-for="(product, index) in topProducts" :key="index" 
                class="mb-4 p-3 rounded-lg transform transition-all duration-300 hover:bg-gray-50">
              <div class="flex justify-between mb-1">
                <span class="font-medium text-gray-800">{{ product.name }}</span>
                <span class="text-gray-600 font-semibold">{{ product.sales }} đã bán</span>
              </div>
              <div class="w-full h-2 bg-gray-100 rounded-full overflow-hidden">
                <div :class="['h-full', product.color]" :style="`width: ${product.percentage}%`"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="lg:col-span-8">
        <div class="bg-white rounded-xl shadow-md p-5 border border-gray-100 h-full">
          <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-bold text-gray-800 flex items-center">
              <i class="pi pi-bolt text-amber-500 mr-2"></i>
              Hành động nhanh
            </h2>
            <Button icon="pi pi-plus" class="p-button-text p-button-rounded" />
          </div>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="bg-gradient-to-br from-blue-500 to-blue-700 rounded-xl p-5 text-white text-center cursor-pointer transition-transform hover:scale-105 hover:shadow-lg">
              <div class="bg-white/20 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <i class="pi pi-user-plus text-3xl"></i>
              </div>
              <h3 class="font-medium">Thêm người dùng</h3>
            </div>
            <div class="bg-gradient-to-br from-emerald-500 to-green-700 rounded-xl p-5 text-white text-center cursor-pointer transition-transform hover:scale-105 hover:shadow-lg">
              <div class="bg-white/20 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <i class="pi pi-plus-circle text-3xl"></i>
              </div>
              <h3 class="font-medium">Sản phẩm mới</h3>
            </div>
            <div class="bg-gradient-to-br from-amber-500 to-orange-700 rounded-xl p-5 text-white text-center cursor-pointer transition-transform hover:scale-105 hover:shadow-lg">
              <div class="bg-white/20 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <i class="pi pi-list text-3xl"></i>
              </div>
              <h3 class="font-medium">Xem đơn hàng</h3>
            </div>
            <div class="bg-gradient-to-br from-purple-500 to-indigo-700 rounded-xl p-5 text-white text-center cursor-pointer transition-transform hover:scale-105 hover:shadow-lg">
              <div class="bg-white/20 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <i class="pi pi-file text-3xl"></i>
              </div>
              <h3 class="font-medium">Tạo báo cáo</h3>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template> 