import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import axios from 'axios'
import { useRouter } from 'vue-router'

export const useAuthStore = defineStore('auth', () => {
  const router = useRouter()
  const user = ref(JSON.parse(localStorage.getItem('adminUser') || 'null'))
  const accessToken = ref(localStorage.getItem('adminAccessToken') || '')
  
  const isAuthenticated = computed(() => !!user.value && !!accessToken.value)
  
  // Set axios default authorization header when token changes
  const setAuthHeader = () => {
    if (accessToken.value) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${accessToken.value}`
    } else {
      delete axios.defaults.headers.common['Authorization']
    }
  }
  
  // Initialize auth header
  setAuthHeader()
  
  // Login function
  const login = async (email: string, password: string) => {
    try {
      const response = await axios.post('http://localhost:3000/api/admin/login', {
        email,
        password
      }, {
        withCredentials: true
      })
      
      user.value = response.data.data.user
      accessToken.value = response.data.data.accessToken
      
      localStorage.setItem('adminUser', JSON.stringify(user.value))
      localStorage.setItem('adminAccessToken', accessToken.value)
      
      setAuthHeader()
      
      return true
    } catch (error) {
      console.error('Login error:', error)
      return false
    }
  }
  
  // Logout function
  const logout = async () => {
    try {
      await axios.post('http://localhost:3000/api/admin/logout', {}, {
        withCredentials: true
      })
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // Clear user data regardless of API success
      user.value = null
      accessToken.value = ''
      
      localStorage.removeItem('adminUser')
      localStorage.removeItem('adminAccessToken')
      
      setAuthHeader()
      
      // Redirect to login
      router.push('/login')
    }
  }
  
  // Get user profile
  const getProfile = async () => {
    try {
      const response = await axios.get('http://localhost:3000/api/admin/profile', {
        withCredentials: true
      })
      
      user.value = response.data.data.user
      localStorage.setItem('adminUser', JSON.stringify(user.value))
      
      return user.value
    } catch (error) {
      console.error('Get profile error:', error)
      return null
    }
  }
  
  return {
    user,
    accessToken,
    isAuthenticated,
    login,
    logout,
    getProfile
  }
}) 