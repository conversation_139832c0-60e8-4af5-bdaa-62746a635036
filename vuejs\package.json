{"name": "v<PERSON><PERSON><PERSON>", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "playwright test", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "axios": "^1.6.7", "marked": "^11.1.1", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue-toastification": "^2.0.0-rc.5"}, "devDependencies": {"@playwright/test": "^1.51.1", "@tsconfig/node22": "^22.0.1", "@types/jsdom": "^21.1.7", "@types/marked": "^5.0.2", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "jsdom": "^26.0.0", "npm-run-all2": "^7.0.2", "postcss": "^8.5.4", "tailwindcss": "^3.4.17", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.1.1", "vue-tsc": "^2.2.8"}}