<template>
  <div class="max-w-3xl mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-8 text-center text-gray-900 dark:text-white">Your Learning Progress</h1>
    
    <div v-if="isLoading" class="text-center py-8 text-gray-600 dark:text-gray-400">
      <div class="flex justify-center items-center py-16">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    </div>
    
    <div v-else-if="error" class="text-center p-4 text-red-600 dark:text-red-400">
      {{ error }}
      <button @click="fetchUserProgress" class="mt-4 px-4 py-2 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors dark:text-gray-200">Retry</button>
    </div>
    
    <div v-else-if="!progress || (!progress.completedLessons && !progress.exerciseResults)" class="text-center py-12">
      <p class="mb-4 text-gray-600 dark:text-gray-400">You haven't started any lessons yet.</p>
      <router-link to="/topics" class="inline-block px-6 py-3 bg-green-500 text-white font-bold rounded-md hover:bg-green-600 transition-colors">
        Start Learning
      </router-link>
    </div>
    
    <div v-else>
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
        <div class="p-6 bg-white dark:bg-dark-card rounded-lg shadow-md text-center">
          <div class="text-3xl font-bold text-blue-500 dark:text-blue-400 mb-2">{{ completedLessonsCount }}</div>
          <div class="text-gray-600 dark:text-gray-400">Lessons Completed</div>
        </div>
        
        <div class="p-6 bg-white dark:bg-dark-card rounded-lg shadow-md text-center">
          <div class="text-3xl font-bold text-blue-500 dark:text-blue-400 mb-2">{{ totalExercises }}</div>
          <div class="text-gray-600 dark:text-gray-400">Exercises Attempted</div>
        </div>
        
        <div class="p-6 bg-white dark:bg-dark-card rounded-lg shadow-md text-center">
          <div class="text-3xl font-bold text-blue-500 dark:text-blue-400 mb-2">{{ correctExercises }}</div>
          <div class="text-gray-600 dark:text-gray-400">Correct Answers</div>
        </div>
        
        <div class="p-6 bg-white dark:bg-dark-card rounded-lg shadow-md text-center">
          <div class="text-3xl font-bold text-blue-500 dark:text-blue-400 mb-2">{{ accuracyPercentage }}%</div>
          <div class="text-gray-600 dark:text-gray-400">Accuracy</div>
        </div>
      </div>
      
      <div class="mb-12">
        <h2 class="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Completed Lessons</h2>
        <div v-if="!progress.completedLessons || progress.completedLessons.length === 0" class="p-8 bg-gray-50 dark:bg-gray-800 rounded-lg text-center text-gray-600 dark:text-gray-400">
          You haven't completed any lessons yet.
        </div>
        <div v-else class="flex flex-col space-y-4 mt-6">
          <div v-for="lessonId in progress.completedLessons" :key="lessonId" class="flex items-center p-4 bg-white dark:bg-dark-card rounded-lg shadow-md">
            <div class="w-10 h-10 flex items-center justify-center rounded-full bg-green-500 text-white font-bold mr-4">✓</div>
            <div class="flex-1">
              <div class="font-bold mb-1 text-gray-800 dark:text-gray-200">Lesson {{ lessonId }}</div>
              <router-link :to="`/lessons/${lessonId}`" class="inline-block px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm">
                Review Lesson
              </router-link>
            </div>
          </div>
        </div>
      </div>
      
      <div>
        <h2 class="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Exercise Results</h2>
        <div v-if="!progress.exerciseResults || progress.exerciseResults.length === 0" class="p-8 bg-gray-50 dark:bg-gray-800 rounded-lg text-center text-gray-600 dark:text-gray-400">
          You haven't attempted any exercises yet.
        </div>
        <div v-else class="flex flex-col space-y-4 mt-6">
          <div v-for="result in progress.exerciseResults" :key="result.exerciseId" class="flex items-center p-4 bg-white dark:bg-dark-card rounded-lg shadow-md">
            <div class="w-10 h-10 flex items-center justify-center rounded-full mr-4" :class="result.correct ? 'bg-green-500 text-white' : 'bg-red-500 text-white'">
              {{ result.correct ? '✓' : '✗' }}
            </div>
            <div class="flex-1">
              <div class="font-bold mb-1 text-gray-800 dark:text-gray-200">Exercise {{ result.exerciseId }}</div>
              <div class="text-sm text-gray-600 dark:text-gray-400">Attempts: {{ result.attemptCount }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { grammarService } from '../../services/grammar.service';

const router = useRouter();

interface ExerciseResult {
  exerciseId: string;
  correct: boolean;
  attemptCount: number;
}

interface UserProgress {
  userId: string;
  completedLessons?: string[];
  exerciseResults?: ExerciseResult[];
}

// Cấu trúc response từ API có thể chứa dữ liệu trong trường data
interface ApiResponse {
  success?: boolean;
  data?: UserProgress;
  status?: string;
  message?: string;
  userId?: string;
  completedLessons?: string[];
  exerciseResults?: ExerciseResult[];
}

const progress = ref<UserProgress | null>(null);
const isLoading = ref(true);
const error = ref('');

const completedLessonsCount = computed(() => {
  return progress.value?.completedLessons?.length || 0;
});

const totalExercises = computed(() => {
  return progress.value?.exerciseResults?.length || 0;
});

const correctExercises = computed(() => {
  return progress.value?.exerciseResults?.filter(result => result.correct)?.length || 0;
});

const accuracyPercentage = computed(() => {
  if (!totalExercises.value) return 0;
  return Math.round((correctExercises.value / totalExercises.value) * 100);
});

const fetchUserProgress = async () => {
  error.value = '';
  isLoading.value = true;
  
  try {
    const response = await grammarService.getUserProgress() as ApiResponse;
    console.log('User progress response:', response);
    
    // Đảm bảo dữ liệu có cấu trúc đúng
    if (response.data) {
      // Nếu dữ liệu nằm trong trường data (từ BaseController)
      progress.value = {
        userId: response.data.userId || '',
        completedLessons: Array.isArray(response.data.completedLessons) ? response.data.completedLessons : [],
        exerciseResults: Array.isArray(response.data.exerciseResults) ? response.data.exerciseResults : []
      };
    } else {
      // Cấu trúc trực tiếp
      progress.value = {
        userId: response.userId || '',
        completedLessons: Array.isArray(response.completedLessons) ? response.completedLessons : [],
        exerciseResults: Array.isArray(response.exerciseResults) ? response.exerciseResults : []
      };
    }
    
    console.log('Processed progress data:', progress.value);
  } catch (err: any) {
    console.error('Error fetching user progress:', err);
    if (err.response?.status === 401) {
      // Redirect to login if unauthorized
      router.push('/login');
    } else {
      error.value = err.response?.data?.message || 'Failed to load your progress.';
    }
  } finally {
    isLoading.value = false;
  }
};

onMounted(fetchUserProgress);
</script>