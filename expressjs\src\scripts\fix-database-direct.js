import { sequelize } from '../config/database.js';

async function fixDatabase() {
  try {
    console.log('Starting database fix...');
    
    // Get a direct connection to execute raw SQL
    const queryInterface = sequelize.getQueryInterface();
    
    // Check if the backup table exists and drop it if it does
    await queryInterface.sequelize.query(
      `DROP TABLE IF EXISTS grammar_topics_backup;`
    );
    
    // Reset the SQLite sequence for grammar_topics table
    const [results] = await queryInterface.sequelize.query(
      'SELECT MAX(id) as maxId FROM grammar_topics;'
    );
    
    const maxId = results[0]?.maxId || 0;
    
    await queryInterface.sequelize.query(
      `DELETE FROM sqlite_sequence WHERE name = 'grammar_topics';`
    );
    
    await queryInterface.sequelize.query(
      `INSERT INTO sqlite_sequence (name, seq) VALUES ('grammar_topics', ${maxId});`
    );
    
    console.log('Database fix completed successfully. Max ID set to:', maxId);
    
    // Close the connection
    await sequelize.close();
    
    process.exit(0);
  } catch (error) {
    console.error('Failed to fix database:', error);
    await sequelize.close();
    process.exit(1);
  }
}

fixDatabase(); 