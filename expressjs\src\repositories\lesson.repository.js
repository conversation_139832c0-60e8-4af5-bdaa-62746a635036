import BaseRepository from './base.repository.js';
import { Lesson } from '../models/index.js';

class LessonRepository extends BaseRepository {
  constructor() {
    super(Lesson);
  }

  /**
   * Find lesson with its exercises
   * @param {number} id - Lesson ID
   * @returns {Promise<Object>} - Lesson with exercises
   */
  async findWithExercises(id) {
    return this.model.findByPk(id, {
      include: ['exercises'],
      where: { isActive: true }
    });
  }

  /**
   * Find lessons by topic ID
   * @param {number} topicId - Topic ID
   * @returns {Promise<Array>} - Array of lessons
   */
  async findByTopicId(topicId) {
    return this.model.findAll({
      where: { 
        topicId,
        isActive: true 
      },
      order: [['order', 'ASC']]
    });
  }
}

export default new LessonRepository(); 