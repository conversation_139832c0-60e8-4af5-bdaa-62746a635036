import axios from 'axios';

const API_URL = 'http://localhost:3000/api';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Enable sending cookies with requests
});

// Request interceptor for adding auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken');
    console.log('Request interceptor - Token:', token);
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    console.log('Request headers:', config.headers);
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for handling errors
api.interceptors.response.use(
  (response) => {
    console.log('Response interceptor:', response.config.url, response.status);
    return response;
  },
  async (error) => {
    console.error('API Error:', error.response?.status, error.config?.url);
    
    // Handle 401 errors (unauthorized)
    if (error.response && error.response.status === 401) {
      console.log('Unauthorized error - attempting token refresh');
      
      // Try to refresh token
      const refreshToken = localStorage.getItem('refreshToken');
      if (refreshToken) {
        try {
          console.log('Refreshing token with:', refreshToken);
          const response = await axios.post(`${API_URL}/auth/refresh-token`, {
            refreshToken,
          }, {
            withCredentials: true
          });
          
          console.log('Token refresh response:', response.data);
          
          // Xử lý cấu trúc dữ liệu trả về
          let newToken;
          if (response.data.data) {
            newToken = response.data.data.accessToken;
          } else {
            newToken = response.data.accessToken;
          }
          
          if (!newToken) {
            throw new Error('No access token received from refresh');
          }
          
          console.log('New token received:', newToken);
          localStorage.setItem('accessToken', newToken);
          
          // Retry the original request
          const originalRequest = error.config;
          originalRequest.headers.Authorization = `Bearer ${newToken}`;
          return api(originalRequest);
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError);
          // If refresh token fails, logout user
          localStorage.removeItem('accessToken');
          localStorage.removeItem('refreshToken');
          window.location.href = '/login?message=Session expired. Please login again.';
        }
      } else {
        console.log('No refresh token available');
        // No refresh token, redirect to login
        window.location.href = '/login?message=Authentication required';
      }
    }
    return Promise.reject(error);
  }
);

export default api; 