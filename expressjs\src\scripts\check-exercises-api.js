import axios from 'axios';
import { sequelize } from '../config/database.js';
import { Exercise } from '../models/index.js';

// Ki<PERSON>m tra dữ liệu exercises trong database
async function checkExercisesInDB() {
  try {
    // Kiểm tra kết nối database
    await sequelize.authenticate();
    console.log('Kết nối database thành công.');

    // Đếm số lượng exercises
    const count = await Exercise.count();
    console.log(`Số lượng exercises trong database: ${count}`);

    // Lấy danh sách tất cả exercises
    const exercises = await Exercise.findAll();
    console.log('Danh sách exercises:');
    exercises.forEach(exercise => {
      console.log(`ID: ${exercise.id}, Title: ${exercise.title}, LessonID: ${exercise.lessonId}`);
    });

    // Đếm số lượng exercises theo lessonId
    const lessonCounts = await Exercise.findAll({
      attributes: ['lessonId', [sequelize.fn('COUNT', sequelize.col('id')), 'count']],
      group: ['lessonId'],
      raw: true
    });
    
    console.log('Số lượng exercises theo bài học:');
    console.log(lessonCounts);

  } catch (error) {
    console.error('Lỗi khi kiểm tra exercises trong DB:', error);
  }
}

// Đăng nhập để lấy token
async function login() {
  try {
    const response = await axios.post('http://localhost:3000/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('Phản hồi đăng nhập đầy đủ:', JSON.stringify(response.data, null, 2));
    
    const data = response.data;
    let token = null;
    
    // Kiểm tra các cấu trúc phản hồi có thể có
    if (data.status === 'success' && data.data && data.data.token) {
      token = data.data.token;
    } else if (data.success && data.data && data.data.token) {
      token = data.data.token;
    } else if (data.data && data.data.user && data.data.accessToken) {
      token = data.data.accessToken;
    }
    
    if (token) {
      console.log('Đăng nhập thành công, token:', token);
      return token;
    } else {
      console.error('Không tìm thấy token trong phản hồi');
      return null;
    }
  } catch (error) {
    console.error('Lỗi khi đăng nhập:', error.message);
    if (error.response) {
      console.error('Dữ liệu phản hồi:', error.response.data);
    }
    return null;
  }
}

// Kiểm tra API exercises
async function checkExercisesAPI(token) {
  if (!token) {
    console.error('Không có token, không thể kiểm tra API exercises');
    return;
  }
  
  try {
    // Kiểm tra API lấy danh sách exercises theo bài học
    const lessonId = 1; // Kiểm tra bài học có ID = 1
    const response = await axios.get(`http://localhost:3000/api/grammar/exercises/lesson/${lessonId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const data = response.data;
    console.log(`API exercises theo bài học ${lessonId}:`, data);
    
    if (data.success) {
      console.log(`Số lượng exercises cho bài học ${lessonId}:`, data.data.length);
    } else {
      console.error('Lỗi khi lấy exercises:', data);
    }
  } catch (error) {
    console.error('Lỗi khi gọi API exercises:', error.message);
    if (error.response) {
      console.error('Dữ liệu phản hồi:', error.response.data);
    }
  }
}

// Chạy kiểm tra
(async () => {
  console.log('=== KIỂM TRA DỮ LIỆU EXERCISES TRONG DATABASE ===');
  await checkExercisesInDB();
  
  console.log('\n=== KIỂM TRA API EXERCISES ===');
  const token = await login();
  if (token) {
    await checkExercisesAPI(token);
  }
  
  // Đóng kết nối database khi hoàn tất
  await sequelize.close();
})(); 