import { Sequelize } from 'sequelize';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get directory name for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Database path
const dbPath = process.env.DB_STORAGE || './database.sqlite';

// Backup database file
const backupDatabase = () => {
  const backupPath = `${dbPath}.backup-${Date.now()}`;
  
  try {
    if (fs.existsSync(dbPath)) {
      fs.copyFileSync(dbPath, backupPath);
      console.log(`Database backup created at ${backupPath}`);
      return backupPath;
    }
  } catch (error) {
    console.error('Failed to create database backup:', error);
    process.exit(1);
  }
  
  return null;
};

// Create a new database connection without using models
const createNewConnection = () => {
  return new Sequelize({
    dialect: 'sqlite',
    storage: dbPath,
    logging: false
  });
};

// Repair database
const repairDatabase = async () => {
  try {
    console.log('Starting database repair...');
    
    // Create backup before repair
    const backupPath = backupDatabase();
    if (!backupPath) {
      console.error('No database file found to repair.');
      process.exit(1);
    }
    
    // Create a new connection
    const sequelize = createNewConnection();
    
    // Test connection
    await sequelize.authenticate();
    console.log('Database connection established successfully.');
    
    // Run VACUUM to rebuild the database file
    console.log('Running VACUUM to rebuild the database...');
    await sequelize.query('VACUUM;');
    console.log('VACUUM completed successfully.');
    
    // Check for corrupted tables
    console.log('Checking database integrity...');
    const [results] = await sequelize.query('PRAGMA integrity_check;');
    
    if (results[0].integrity_check === 'ok') {
      console.log('Database integrity check passed.');
    } else {
      console.error('Database integrity issues found:', results);
      console.log('Attempting to recover from backup...');
      
      // If integrity check fails, restore from backup
      fs.copyFileSync(backupPath, dbPath);
      console.log('Database restored from backup.');
      
      console.log('Please run the db-init.js script to initialize the database.');
      process.exit(1);
    }
    
    console.log('Database repair completed successfully.');
    console.log('Please run the db-init.js script to initialize the database models.');
    
    await sequelize.close();
    process.exit(0);
  } catch (error) {
    console.error('Database repair failed:', error);
    process.exit(1);
  }
};

// Run repair
repairDatabase(); 