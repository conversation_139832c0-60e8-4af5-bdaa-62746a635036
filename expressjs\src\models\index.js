import { sequelize } from '../config/database.js';
import User from './user.model.js';
import GrammarTopic from './grammarTopic.model.js';
import Lesson from './lesson.model.js';
import Exercise from './exercise.model.js';
import UserProgress from './userProgress.model.js';
import Quiz from './quiz.model.js';
import QuizAttempt from './quizAttempt.model.js';
import Achievement from './achievement.model.js';
import UserAchievement from './userAchievement.model.js';
import LearningPath from './learningPath.model.js';
import UserLearningPath from './userLearningPath.model.js';

// Define associations between models
GrammarTopic.hasMany(Lesson, { foreignKey: 'topicId', as: 'lessons' });
Lesson.belongsTo(GrammarTopic, { foreignKey: 'topicId', as: 'topic' });

Lesson.hasMany(Exercise, { foreignKey: 'lessonId', as: 'exercises' });
Exercise.belongsTo(Lesson, { foreignKey: 'lessonId', as: 'lesson' });

GrammarTopic.hasMany(Quiz, { foreignKey: 'topicId', as: 'quizzes' });
Quiz.belongsTo(GrammarTopic, { foreignKey: 'topicId', as: 'topic' });

User.hasMany(UserProgress, { foreignKey: 'userId', as: 'progress' });
UserProgress.belongsTo(User, { foreignKey: 'userId', as: 'user' });

Lesson.hasMany(UserProgress, { foreignKey: 'lessonId', as: 'userProgress' });
UserProgress.belongsTo(Lesson, { foreignKey: 'lessonId', as: 'lesson' });

Exercise.hasMany(UserProgress, { foreignKey: 'exerciseId', as: 'userProgress' });
UserProgress.belongsTo(Exercise, { foreignKey: 'exerciseId', as: 'exercise' });

User.hasMany(QuizAttempt, { foreignKey: 'userId', as: 'quizAttempts' });
QuizAttempt.belongsTo(User, { foreignKey: 'userId', as: 'user' });

Quiz.hasMany(QuizAttempt, { foreignKey: 'quizId', as: 'attempts' });
QuizAttempt.belongsTo(Quiz, { foreignKey: 'quizId', as: 'quiz' });

// Achievement associations
User.belongsToMany(Achievement, { 
  through: UserAchievement,
  foreignKey: 'userId',
  as: 'achievements' 
});

Achievement.belongsToMany(User, { 
  through: UserAchievement,
  foreignKey: 'achievementId',
  as: 'users' 
});

User.hasMany(UserAchievement, { foreignKey: 'userId', as: 'userAchievements' });
UserAchievement.belongsTo(User, { foreignKey: 'userId', as: 'user' });

Achievement.hasMany(UserAchievement, { foreignKey: 'achievementId', as: 'userAchievements' });
UserAchievement.belongsTo(Achievement, { foreignKey: 'achievementId', as: 'achievement' });

// Learning Path associations
User.belongsToMany(LearningPath, { 
  through: UserLearningPath,
  foreignKey: 'userId',
  as: 'learningPaths' 
});

LearningPath.belongsToMany(User, { 
  through: UserLearningPath,
  foreignKey: 'learningPathId',
  as: 'users' 
});

User.hasMany(UserLearningPath, { foreignKey: 'userId', as: 'userLearningPaths' });
UserLearningPath.belongsTo(User, { foreignKey: 'userId', as: 'user' });

LearningPath.hasMany(UserLearningPath, { foreignKey: 'learningPathId', as: 'userLearningPaths' });
UserLearningPath.belongsTo(LearningPath, { foreignKey: 'learningPathId', as: 'learningPath' });

export { 
  sequelize, 
  User,
  GrammarTopic,
  Lesson,
  Exercise,
  UserProgress,
  Quiz,
  QuizAttempt,
  Achievement,
  UserAchievement,
  LearningPath,
  UserLearningPath
};
