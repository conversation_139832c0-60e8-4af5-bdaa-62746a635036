import BaseService from './base.service.js';
import { grammarTopicRepository } from '../repositories/index.js';

class GrammarTopicService extends BaseService {
  constructor() {
    super(grammarTopicRepository);
  }

  /**
   * Get all active grammar topics
   * @returns {Promise<Array>} - Array of grammar topics
   */
  async getAllActiveTopics() {
    return this.repository.findAllActive();
  }

  /**
   * Get grammar topics by level
   * @param {string} level - Level (beginner, intermediate, advanced)
   * @returns {Promise<Array>} - Array of grammar topics
   */
  async getTopicsByLevel(level) {
    if (!['beginner', 'intermediate', 'advanced'].includes(level)) {
      throw new Error('Invalid level. Must be beginner, intermediate, or advanced');
    }
    return this.repository.findByLevel(level);
  }

  /**
   * Get grammar topic with its lessons
   * @param {number} id - Topic ID
   * @returns {Promise<Object>} - Grammar topic with lessons
   */
  async getTopicWithLessons(id) {
    const topic = await this.repository.findWithLessons(id);
    if (!topic) {
      throw new Error(`Grammar topic with id ${id} not found`);
    }
    return topic;
  }
}

export default new GrammarTopicService(); 