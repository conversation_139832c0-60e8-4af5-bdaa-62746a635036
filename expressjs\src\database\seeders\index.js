import { sequelize } from '../../config/database.js';
import * as models from '../../models/index.js';
import { seed as seedUsers } from './01-users.js';
import { seed as seedGrammarTopics } from './02-grammar-topics.js';
import { seed as seedLessons } from './03-lessons.js';
import { seed as seedExercises } from './04-exercises.js';

async function runSeeders() {
  try {
    console.log('Seeding database...');
    
    // Đảm bảo kết nối đến database
    await sequelize.authenticate();
    console.log('Database connection established successfully.');
    
    // Đ<PERSON>ng bộ hóa các model với database
    console.log('Synchronizing models with database...');
    await sequelize.sync({ force: true });
    console.log('Models synchronized successfully.');
    
    // Chạy các seeders theo thứ tự
    console.log('Inserting new data...');
    
    console.log('Seeding users...');
    await seedUsers(models);
    
    console.log('Seeding grammar topics...');
    await seedGrammarTopics(models);
    
    console.log('Seeding lessons...');
    try {
      await seedLessons(models);
    } catch (error) {
      console.error('Error seeding lessons:', error);
    }
    
    console.log('Seeding exercises...');
    try {
      await seedExercises(models);
    } catch (error) {
      console.error('Error seeding exercises:', error);
    }
    
    // Kiểm tra dữ liệu đã được thêm vào
    console.log('\nChecking seeded data:');
    const userCount = await models.User.count();
    console.log(`Users: ${userCount}`);
    
    const topicCount = await models.GrammarTopic.count();
    console.log(`Grammar Topics: ${topicCount}`);
    
    const lessonCount = await models.Lesson.count();
    console.log(`Lessons: ${lessonCount}`);
    
    const exerciseCount = await models.Exercise.count();
    console.log(`Exercises: ${exerciseCount}`);
    
    console.log('All seeders completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error running seeders:', error);
    process.exit(1);
  }
}

// Chạy seeders
runSeeders(); 