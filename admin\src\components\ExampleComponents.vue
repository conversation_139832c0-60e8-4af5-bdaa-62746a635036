<script setup lang="ts">
import { ref } from 'vue';

// Example data
const items = ref([
  { id: 1, name: 'Product A', category: 'Electronics', price: 299.99, status: 'In Stock' },
  { id: 2, name: 'Product B', category: 'Clothing', price: 59.99, status: 'Low Stock' },
  { id: 3, name: 'Product C', category: 'Home', price: 129.99, status: 'Out of Stock' },
  { id: 4, name: 'Product D', category: 'Electronics', price: 499.99, status: 'In Stock' },
  { id: 5, name: 'Product E', category: 'Clothing', price: 79.99, status: 'In Stock' },
]);

const selectedItem = ref(null);
const visible = ref(false);
const date = ref(new Date());
const checked = ref(false);
const selectedOption = ref(null);
const options = ref([
  { name: 'Option 1', value: 1 },
  { name: 'Option 2', value: 2 },
  { name: 'Option 3', value: 3 },
]);

const showDialog = () => {
  visible.value = true;
};

const menuItems = ref([
  {
    label: 'Dashboard',
    icon: 'pi pi-fw pi-home',
  },
  {
    label: 'Products',
    icon: 'pi pi-fw pi-shopping-cart',
  },
  {
    label: 'Users',
    icon: 'pi pi-fw pi-users',
  },
  {
    label: 'Settings',
    icon: 'pi pi-fw pi-cog',
  },
]);
</script>

<template>
  <div class="example-components p-4">
    <h1 class="text-3xl font-bold mb-6">PrimeVue Component Examples</h1>
    
    <div class="grid">
      <!-- Basic Components Section -->
      <div class="col-12 md:col-6 lg:col-4 mb-4">
        <Card class="h-full">
          <template #title>Basic Components</template>
          <template #content>
            <div class="flex flex-column gap-3">
              <div>
                <label for="input" class="block mb-2">Input</label>
                <InputText id="input" v-model="selectedItem" class="w-full" placeholder="Enter text" />
              </div>
              
              <div>
                <label for="dropdown" class="block mb-2">Dropdown</label>
                <Dropdown id="dropdown" v-model="selectedOption" :options="options" optionLabel="name" placeholder="Select an option" class="w-full" />
              </div>
              
              <div class="flex align-items-center">
                <Checkbox v-model="checked" :binary="true" id="checkbox" />
                <label for="checkbox" class="ml-2">Checkbox</label>
              </div>
              
              <div>
                <label for="calendar" class="block mb-2">Calendar</label>
                <Calendar id="calendar" v-model="date" showIcon class="w-full" />
              </div>
              
              <Button label="Open Dialog" icon="pi pi-external-link" @click="showDialog" />
            </div>
          </template>
        </Card>
      </div>
      
      <!-- Data Display Section -->
      <div class="col-12 md:col-6 lg:col-8 mb-4">
        <Card class="h-full">
          <template #title>Data Table</template>
          <template #content>
            <DataTable :value="items" :selection="selectedItem" @selection-change="event => selectedItem = event.value" selectionMode="single" 
                      dataKey="id" paginator :rows="5" :rowsPerPageOptions="[5, 10, 20]"
                      responsiveLayout="scroll" class="mb-4">
              <Column field="id" header="ID" sortable></Column>
              <Column field="name" header="Name" sortable></Column>
              <Column field="category" header="Category" sortable></Column>
              <Column field="price" header="Price" sortable>
                <template #body="slotProps">
                  {{ new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(slotProps.data.price) }}
                </template>
              </Column>
              <Column field="status" header="Status" sortable>
                <template #body="slotProps">
                  <Tag :severity="getStatusSeverity(slotProps.data.status)" :value="slotProps.data.status" />
                </template>
              </Column>
              <Column header="Actions">
                <template #body>
                  <Button icon="pi pi-pencil" class="p-button-rounded p-button-success mr-2" />
                  <Button icon="pi pi-trash" class="p-button-rounded p-button-danger" />
                </template>
              </Column>
            </DataTable>
          </template>
        </Card>
      </div>
      
      <!-- Navigation Section -->
      <div class="col-12 mb-4">
        <Card>
          <template #title>Navigation</template>
          <template #content>
            <Menubar :model="menuItems" class="mb-4" />
            
            <div class="grid">
              <div class="col-12 md:col-6">
                <TabView>
                  <TabPanel header="Tab 1">
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                  </TabPanel>
                  <TabPanel header="Tab 2">
                    <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                  </TabPanel>
                  <TabPanel header="Tab 3">
                    <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
                  </TabPanel>
                </TabView>
              </div>
              
              <div class="col-12 md:col-6">
                <Accordion>
                  <AccordionTab header="Accordion 1">
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                  </AccordionTab>
                  <AccordionTab header="Accordion 2">
                    <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                  </AccordionTab>
                  <AccordionTab header="Accordion 3">
                    <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
                  </AccordionTab>
                </Accordion>
              </div>
            </div>
          </template>
        </Card>
      </div>
    </div>
    
    <Dialog :visible="visible" @update:visible="visible = $event" header="Dialog Example" :modal="true" :style="{ width: '50vw' }">
      <p>This is an example dialog using PrimeVue components. It's fully responsive and customizable.</p>
      <template #footer>
        <Button label="Cancel" icon="pi pi-times" @click="visible = false" class="p-button-text" />
        <Button label="Save" icon="pi pi-check" @click="visible = false" autofocus />
      </template>
    </Dialog>
    
    <Toast />
    <ConfirmDialog />
  </div>
</template>

<script lang="ts">
// Add non-setup script for methods that need to be defined
export default {
  methods: {
    getStatusSeverity(status: string) {
      switch (status) {
        case 'In Stock':
          return 'success';
        case 'Low Stock':
          return 'warning';
        case 'Out of Stock':
          return 'danger';
        default:
          return null;
      }
    }
  }
}
</script> 