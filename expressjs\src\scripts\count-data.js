import { sequelize } from '../config/database.js';
import * as models from '../models/index.js';

async function countData() {
  try {
    // <PERSON><PERSON><PERSON> b<PERSON>o kết n<PERSON>i đến database
    await sequelize.authenticate();
    console.log('Database connection established successfully.');
    
    // Đ<PERSON><PERSON> số lượng bản ghi trong mỗi bảng
    const userCount = await models.User.count();
    console.log(`Users: ${userCount}`);
    
    const topicCount = await models.GrammarTopic.count();
    console.log(`Grammar Topics: ${topicCount}`);
    
    const lessonCount = await models.Lesson.count();
    console.log(`Lessons: ${lessonCount}`);
    
    const exerciseCount = await models.Exercise.count();
    console.log(`Exercises: ${exerciseCount}`);
    
    // L<PERSON>y danh sách các grammar topics
    console.log('\nGrammar Topics:');
    const topics = await models.GrammarTopic.findAll({
      attributes: ['id', 'name', 'level']
    });
    topics.forEach(topic => {
      console.log(`${topic.id}: ${topic.name} (${topic.level})`);
    });
    
    // Lấy danh sách các lesson của topic đầu tiên
    if (topics.length > 0) {
      const firstTopicId = topics[0].id;
      console.log(`\nLessons for Topic ${firstTopicId}:`);
      const lessons = await models.Lesson.findAll({
        where: { topicId: firstTopicId },
        attributes: ['id', 'title', 'order']
      });
      lessons.forEach(lesson => {
        console.log(`${lesson.id}: ${lesson.title} (Order: ${lesson.order})`);
      });
      
      // Lấy danh sách các exercise của lesson đầu tiên
      if (lessons.length > 0) {
        const firstLessonId = lessons[0].id;
        console.log(`\nExercises for Lesson ${firstLessonId}:`);
        const exercises = await models.Exercise.findAll({
          where: { lessonId: firstLessonId },
          attributes: ['id', 'title', 'type', 'difficulty']
        });
        exercises.forEach(exercise => {
          console.log(`${exercise.id}: ${exercise.title} (${exercise.type}, ${exercise.difficulty})`);
        });
      }
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error counting data:', error);
    process.exit(1);
  }
}

// Chạy function đếm
countData(); 