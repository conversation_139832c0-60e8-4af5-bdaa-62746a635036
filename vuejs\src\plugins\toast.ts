import Toast, { POSITION, TYPE } from 'vue-toastification';
import type { PluginOptions } from 'vue-toastification';
import 'vue-toastification/dist/index.css';

const options: PluginOptions = {
  position: POSITION.TOP_RIGHT,
  timeout: 5000,
  closeOnClick: true,
  pauseOnFocusLoss: true,
  pauseOnHover: true,
  draggable: true,
  draggablePercent: 0.6,
  showCloseButtonOnHover: false,
  hideProgressBar: false,
  closeButton: 'button',
  icon: true,
  rtl: false,
  transition: 'Vue-Toastification__bounce',
  maxToasts: 5,
  newestOnTop: true,
  toastClassName: 'custom-toast',
  bodyClassName: ['custom-toast-body'],
  filterBeforeCreate: (toast, toasts) => {
    if (toasts.filter(
      t => t.type === toast.type && 
      t.content === toast.content
    ).length !== 0) {
      // Returning false discards the toast
      return false;
    }
    // You can modify the toast before returning it
    return toast;
  }
};

// Add custom CSS to handle dark mode
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = `
    .Vue-Toastification__toast.custom-toast {
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    
    @media (prefers-color-scheme: dark) {
      .Vue-Toastification__toast.custom-toast {
        background-color: #2d3748;
        color: #f7fafc;
      }
      
      .Vue-Toastification__toast.custom-toast.Vue-Toastification__toast--success {
        background-color: #22543d;
      }
      
      .Vue-Toastification__toast.custom-toast.Vue-Toastification__toast--error {
        background-color: #742a2a;
      }
      
      .Vue-Toastification__toast.custom-toast.Vue-Toastification__toast--warning {
        background-color: #744210;
      }
      
      .Vue-Toastification__toast.custom-toast.Vue-Toastification__toast--info {
        background-color: #2a4365;
      }
      
      .Vue-Toastification__progress-bar {
        background-color: rgba(255, 255, 255, 0.3);
      }
    }
    
    html.dark .Vue-Toastification__toast.custom-toast {
      background-color: #2d3748;
      color: #f7fafc;
    }
    
    html.dark .Vue-Toastification__toast.custom-toast.Vue-Toastification__toast--success {
      background-color: #22543d;
    }
    
    html.dark .Vue-Toastification__toast.custom-toast.Vue-Toastification__toast--error {
      background-color: #742a2a;
    }
    
    html.dark .Vue-Toastification__toast.custom-toast.Vue-Toastification__toast--warning {
      background-color: #744210;
    }
    
    html.dark .Vue-Toastification__toast.custom-toast.Vue-Toastification__toast--info {
      background-color: #2a4365;
    }
    
    html.dark .Vue-Toastification__progress-bar {
      background-color: rgba(255, 255, 255, 0.3);
    }
  `;
  document.head.appendChild(style);
}

export { Toast, options }; 