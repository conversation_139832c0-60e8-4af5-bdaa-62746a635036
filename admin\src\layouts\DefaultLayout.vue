<script setup>
import Header from './components/Header.vue'
import Footer from './components/Footer.vue'
import { useThemeStore } from '../stores/theme'
import { computed } from 'vue'

const themeStore = useThemeStore()
const isDark = computed(() => themeStore.isDark)
</script>

<template>
  <div class="layout" :class="{ 'dark-mode': isDark }">
    <Header />
    
    <main class="main-content">
      <div class="content-container">
        <slot></slot>
      </div>
    </main>
    
    <Footer />
  </div>
</template>

<style scoped>
.layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f3f4f6;
  color: #1f2937;
  transition: background-color 0.3s, color 0.3s;
}

.main-content {
  flex-grow: 1;
  padding: 2rem 0;
}

.content-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Dark mode */
.dark-mode {
  background-color: #111827;
  color: #f9fafb;
}

/* Make layout take at least full viewport height */
:deep(body), :deep(html) {
  height: 100%;
  margin: 0;
  padding: 0;
}
</style> 