import { defineStore } from 'pinia'
import { ref, watch } from 'vue'

export const useThemeStore = defineStore('theme', () => {
  // State
  const isDarkMode = ref(false)
  
  // Khởi tạo theme từ localStorage hoặc theo cài đặt hệ thống
  const initTheme = () => {
    // Kiểm tra localStorage trước
    const savedTheme = localStorage.getItem('theme')
    if (savedTheme) {
      isDarkMode.value = savedTheme === 'dark'
    } else {
      // Nếu không có trong localStorage, kiểm tra cài đặt hệ thống
      isDarkMode.value = window.matchMedia('(prefers-color-scheme: dark)').matches
    }
    
    // Áp dụng theme
    applyTheme()
  }
  
  // Áp dụng theme vào document
  const applyTheme = () => {
    if (isDarkMode.value) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
    
    // Lưu vào localStorage
    localStorage.setItem('theme', isDarkMode.value ? 'dark' : 'light')
  }
  
  // Toggle theme
  const toggleTheme = () => {
    isDarkMode.value = !isDarkMode.value
    applyTheme()
  }
  
  // Theo dõi thay đổi cài đặt hệ thống
  const setupSystemThemeListener = () => {
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
      // Chỉ áp dụng khi người dùng chưa chọn theme
      if (!localStorage.getItem('theme')) {
        isDarkMode.value = event.matches
        applyTheme()
      }
    })
  }
  
  // Khởi tạo
  if (typeof window !== 'undefined') {
    initTheme()
    setupSystemThemeListener()
  }
  
  return {
    isDarkMode,
    toggleTheme
  }
}) 