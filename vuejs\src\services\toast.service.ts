import { useToast } from 'vue-toastification';

const toast = useToast();

// Default options for all toasts
const defaultOptions: any = {
  timeout: 5000,
  closeOnClick: true,
  pauseOnFocusLoss: true,
  pauseOnHover: true,
  draggable: true,
  showCloseButtonOnHover: true,
};

export const toastService = {
  success(message: string, options?: any) {
    toast.success(message, { ...defaultOptions, ...options });
  },

  error(message: string, options?: any) {
    toast.error(message, { 
      ...defaultOptions, 
      timeout: 8000,  // Errors stay longer by default
      ...options 
    });
  },

  info(message: string, options?: any) {
    toast.info(message, { ...defaultOptions, ...options });
  },

  warning(message: string, options?: any) {
    toast.warning(message, { ...defaultOptions, ...options });
  },
  
  // Clear all toasts
  clear() {
    toast.clear();
  },
  
  // Dismiss a specific toast by ID
  dismiss(id: string | number) {
    toast.dismiss(id);
  }
}; 