import bcrypt from 'bcryptjs';

export async function seed(db) {
  const hashedPassword = await bcrypt.hash('password123', 10);
  
  // Tạo dữ liệu mẫu
  await db.User.bulkCreate([
    {
      username: 'admin',
      email: '<EMAIL>',
      password: hashedPassword,
      roles: 'admin',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      username: 'teacher',
      email: '<EMAIL>',
      password: hashedPassword,
      roles: 'teacher',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      username: 'student',
      email: '<EMAIL>',
      password: hashedPassword,
      roles: 'user',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      username: 'john_doe',
      email: '<EMAIL>',
      password: hashedPassword,
      roles: 'user',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      username: 'jane_doe',
      email: '<EMAIL>',
      password: hashedPassword,
      roles: 'user',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ]);
  
  console.log('Users seeded successfully');
} 