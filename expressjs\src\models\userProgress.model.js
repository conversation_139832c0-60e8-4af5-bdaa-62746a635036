import { DataTypes, Model } from 'sequelize';
import { sequelize } from '../config/database.js';

class UserProgress extends Model {}

UserProgress.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  lessonId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'lessons',
      key: 'id'
    }
  },
  exerciseId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'exercises',
      key: 'id'
    }
  },
  status: {
    type: DataTypes.ENUM('not_started', 'in_progress', 'completed'),
    allowNull: false,
    defaultValue: 'not_started'
  },
  score: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 0,
      max: 100
    }
  },
  answers: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'JSON string containing user answers'
  },
  timeSpent: {
    type: DataTypes.INTEGER, // in seconds
    allowNull: true,
    defaultValue: 0
  },
  lastAccessedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  completedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  createdAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updatedAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  sequelize,
  modelName: 'UserProgress',
  tableName: 'user_progress',
  timestamps: true,
  indexes: [
    {
      name: 'user_lesson_idx',
      fields: ['userId', 'lessonId']
    },
    {
      name: 'user_exercise_idx',
      fields: ['userId', 'exerciseId']
    }
  ]
});

export default UserProgress; 