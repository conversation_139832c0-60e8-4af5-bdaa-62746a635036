{"name": "expressjs-mvc-app", "version": "1.0.0", "description": "Express.js application with MVC architecture, repository pattern, and Sequelize ORM", "type": "module", "main": "src/app.js", "scripts": {"start": "node --experimental-specifier-resolution=node --experimental-modules index.js", "dev": "nodemon --experimental-specifier-resolution=node --experimental-modules index.js", "test": "echo \"Error: no test specified\" && exit 1", "migrate": "sequelize-cli --config src/config/sequelize-cli.config.cjs db:migrate", "migrate:undo": "sequelize-cli --config src/config/sequelize-cli.config.cjs db:migrate:undo", "migrate:undo:all": "sequelize-cli --config src/config/sequelize-cli.config.cjs db:migrate:undo:all", "m": "node --experimental-specifier-resolution=node --experimental-modules src/utils/migrate.js", "migrate-all": "node --experimental-specifier-resolution=node --experimental-modules src/utils/migrate-all.js", "seed": "node --experimental-specifier-resolution=node --experimental-modules src/database/seeders/index.js", "check-data": "node --experimental-specifier-resolution=node --experimental-modules src/scripts/check-data.js", "count-data": "node --experimental-specifier-resolution=node --experimental-modules src/scripts/count-data.js", "reset-db": "node --experimental-specifier-resolution=node --experimental-modules src/scripts/reset-db.js", "init-db": "npm run reset-db && npm run seed", "db:init": "node --experimental-specifier-resolution=node --experimental-modules src/scripts/db-init.js", "db:repair": "node --experimental-specifier-resolution=node --experimental-modules src/scripts/db-repair.js", "db:reset": "if (Test-Path database.sqlite) { Remove-Item database.sqlite } ; npm run db:init"}, "nodemonConfig": {"execMap": {"js": "node --experimental-specifier-resolution=node --experimental-modules"}}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "sequelize": "^6.32.1", "sequelize-auto-migrations-v2": "^1.2.1", "sequelize-cli": "^6.6.1", "sqlite3": "^5.1.6", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}}