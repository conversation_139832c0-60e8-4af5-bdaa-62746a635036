'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      // Get the current max ID from the grammar_topics table
      const [results] = await queryInterface.sequelize.query(
        'SELECT MAX(id) as maxId FROM grammar_topics;'
      );
      
      const maxId = results[0]?.maxId || 0;
      
      // Reset the SQLite sequence for grammar_topics table
      await queryInterface.sequelize.query(
        `DELETE FROM sqlite_sequence WHERE name = 'grammar_topics';`
      );
      
      await queryInterface.sequelize.query(
        `INSERT INTO sqlite_sequence (name, seq) VALUES ('grammar_topics', ${maxId});`
      );
      
      console.log('Successfully fixed grammar_topics table ID sequence');
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    // This is a fix migration, no need for down migration
  }
}; 