import BaseService from './base.service.js';
import { exerciseRepository } from '../repositories/index.js';

class ExerciseService extends BaseService {
  constructor() {
    super(exerciseRepository);
  }

  /**
   * Get exercises by lesson ID
   * @param {number} lessonId - Lesson ID
   * @returns {Promise<Array>} - Array of exercises
   */
  async getExercisesByLesson(lessonId) {
    return this.repository.findByLessonId(lessonId);
  }

  /**
   * Get exercises by difficulty
   * @param {string} difficulty - Difficulty level (easy, medium, hard)
   * @returns {Promise<Array>} - Array of exercises
   */
  async getExercisesByDifficulty(difficulty) {
    if (!['easy', 'medium', 'hard'].includes(difficulty)) {
      throw new Error('Invalid difficulty. Must be easy, medium, or hard');
    }
    return this.repository.findByDifficulty(difficulty);
  }

  /**
   * Validate user's answer for an exercise
   * @param {number} exerciseId - Exercise ID
   * @param {Object} userAnswers - User's answers
   * @returns {Promise<Object>} - Validation result with score
   */
  async validateAnswer(exerciseId, userAnswers) {
    const exercise = await this.repository.findById(exerciseId);
    
    if (!exercise) {
      throw new Error(`Exercise with id ${exerciseId} not found`);
    }
    
    const correctAnswers = JSON.parse(exercise.answers);
    let score = 0;
    let feedback = [];
    
    // This is a simplified scoring logic. In a real app, this would be more complex
    // based on the exercise type (multiple choice, fill in the blanks, etc.)
    if (Array.isArray(correctAnswers)) {
      const totalQuestions = correctAnswers.length;
      let correctCount = 0;
      
      correctAnswers.forEach((answer, index) => {
        const userAnswer = userAnswers[index];
        const isCorrect = this._compareAnswers(answer, userAnswer);
        
        if (isCorrect) {
          correctCount++;
        }
        
        feedback.push({
          questionIndex: index,
          isCorrect,
          correctAnswer: answer
        });
      });
      
      score = Math.round((correctCount / totalQuestions) * 100);
    }
    
    return {
      score,
      feedback,
      passed: score >= 70
    };
  }
  
  /**
   * Compare user answer with correct answer
   * @private
   * @param {*} correctAnswer - Correct answer
   * @param {*} userAnswer - User's answer
   * @returns {boolean} - Whether the answer is correct
   */
  _compareAnswers(correctAnswer, userAnswer) {
    // Simple string comparison for now
    // In a real app, this would handle different answer types and formats
    if (typeof correctAnswer === 'string' && typeof userAnswer === 'string') {
      return correctAnswer.toLowerCase() === userAnswer.toLowerCase();
    }
    
    return JSON.stringify(correctAnswer) === JSON.stringify(userAnswer);
  }
}

export default new ExerciseService(); 