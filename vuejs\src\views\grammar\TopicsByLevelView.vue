<template>
  <div class="max-w-6xl mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-6 text-gray-900 dark:text-white">{{ capitalizedLevel }} Level Grammar Topics</h1>
    
    <div v-if="!isAuthenticated" class="mb-8 p-4 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg text-blue-600 dark:text-blue-300">
      <div class="flex items-center">
        <span class="mr-3 text-xl">ℹ️</span>
        <div>
          <p class="font-medium">You're browsing as a guest</p>
          <p class="text-sm mt-1">Please <router-link to="/login" class="font-bold underline">login</router-link> or <router-link to="/register" class="font-bold underline">register</router-link> to access lessons and exercises.</p>
        </div>
      </div>
    </div>
    
    <div class="flex flex-col sm:flex-row justify-between items-center mb-8 gap-4">
      <router-link to="/topics" class="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
        &larr; All Topics
      </router-link>
      
      <div class="flex gap-4">
        <router-link 
          v-for="level in levels" 
          :key="level" 
          :to="`/topics/${level}`" 
          class="px-4 py-2 rounded-md transition-colors"
          :class="currentLevel === level 
            ? 'bg-blue-500 text-white' 
            : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600'"
        >
          {{ level.charAt(0).toUpperCase() + level.slice(1) }}
        </router-link>
      </div>
    </div>
    
    <div v-if="isLoading" class="text-center py-8 text-gray-600 dark:text-gray-400">
      Loading topics...
    </div>
    
    <div v-else-if="error" class="text-center p-4 text-red-600 dark:text-red-400">
      {{ error }}
      <button @click="fetchTopics" class="mt-4 px-4 py-2 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors dark:text-gray-200">Retry</button>
    </div>
    
    <div v-else-if="topics.length === 0" class="text-center py-8 text-gray-600 dark:text-gray-400">
      No topics found for {{ currentLevel }} level.
    </div>
    
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div v-for="topic in topics" :key="topic.id" class="p-6 bg-white dark:bg-dark-card rounded-lg shadow-md hover:-translate-y-1 transition-transform duration-300">
        <h3 class="text-xl font-bold mb-2 text-gray-800 dark:text-gray-200">{{ topic.title }}</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">{{ topic.description }}</p>
        <router-link :to="`/topics/${topic.id}/lessons`" class="inline-block px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors">
          View Lessons
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import { grammarService } from '../../services/grammar.service';
import { authService } from '../../services/auth.service';

const route = useRoute();

interface GrammarTopic {
  id: string;
  title: string;
  description: string;
  level: string;
  imageUrl?: string;
}

const topics = ref<GrammarTopic[]>([]);
const isLoading = ref(true);
const error = ref('');
const levels = ['beginner', 'intermediate', 'advanced'];

// Authentication status
const isAuthenticated = computed(() => {
  return authService.isAuthenticated();
});

const currentLevel = computed(() => {
  return route.params.level as string;
});

const capitalizedLevel = computed(() => {
  return currentLevel.value.charAt(0).toUpperCase() + currentLevel.value.slice(1);
});

const fetchTopics = async () => {
  error.value = '';
  isLoading.value = true;
  
  try {
    topics.value = await grammarService.getTopicsByLevel(currentLevel.value);
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to load grammar topics.';
  } finally {
    isLoading.value = false;
  }
};

onMounted(fetchTopics);

// Watch for route changes to reload topics when level changes
watch(() => route.params.level, fetchTopics);
</script>