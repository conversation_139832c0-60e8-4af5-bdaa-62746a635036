<script setup>
import { RouterLink } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { useThemeStore } from '../../stores/theme'
import { computed } from 'vue'
import ThemeToggle from '../../components/ThemeToggle.vue'

const authStore = useAuthStore()
const themeStore = useThemeStore()
const user = computed(() => authStore.user)

const handleLogout = () => {
  authStore.logout()
}
</script>

<template>
  <div class="header">
    <div class="header-container">
      <div class="header-left">
        <h1 class="logo">Admin Dashboard</h1>
      </div>
      
      <div class="header-center">
        <nav class="nav">
          <RouterLink to="/" class="nav-link">Dashboard</RouterLink>
          <RouterLink to="/components" class="nav-link">Components</RouterLink>
        </nav>
      </div>
      
      <div class="header-right">
        <div class="user-menu">
          <ThemeToggle />
          <div class="user-info">
            <span class="username">{{ user?.username || user?.email }}</span>
            <span class="user-role">Administrator</span>
          </div>
          <button @click="handleLogout" class="logout-button">
            <span class="logout-icon">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15M12 9l-3 3m0 0 3 3m-3-3h7.5" />
              </svg>
            </span>
            Logout
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.header {
  background-color: #1f2937;
  color: white;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-container {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left, .header-right {
  flex: 1;
}

.header-center {
  flex: 2;
  display: flex;
  justify-content: center;
}

.logo {
  color: #ef4444;
  font-size: 1.25rem;
  font-weight: bold;
}

.nav {
  display: flex;
  align-items: center;
}

.nav-link {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  margin: 0 0.25rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.router-link-active {
  background-color: rgba(255, 255, 255, 0.15);
  font-weight: bold;
}

.user-menu {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 1rem;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.username {
  font-weight: bold;
}

.user-role {
  font-size: 0.75rem;
  opacity: 0.8;
}

.logout-button {
  display: flex;
  align-items: center;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 0.25rem;
  padding: 0.35rem 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.logout-button:hover {
  background-color: #dc2626;
}

.logout-icon {
  display: inline-flex;
  margin-right: 0.35rem;
}

.w-5 {
  width: 1.25rem;
}

.h-5 {
  height: 1.25rem;
}

/* Dark mode overrides */
:deep(.dark-mode) .header {
  background-color: #000;
  border-bottom: 1px solid #374151;
}
</style> 