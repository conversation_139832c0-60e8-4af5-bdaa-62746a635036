import BaseRepository from './base.repository.js';
import { GrammarTopic } from '../models/index.js';

class GrammarTopicRepository extends BaseRepository {
  constructor() {
    super(GrammarTopic);
  }

  /**
   * Find all active grammar topics
   * @returns {Promise<Array>} - Array of grammar topics
   */
  async findAllActive() {
    return this.model.findAll({
      where: { isActive: true },
      order: [['order', 'ASC']]
    });
  }

  /**
   * Find grammar topics by level
   * @param {string} level - Level (beginner, intermediate, advanced)
   * @returns {Promise<Array>} - Array of grammar topics
   */
  async findByLevel(level) {
    return this.model.findAll({
      where: { 
        level,
        isActive: true 
      },
      order: [['order', 'ASC']]
    });
  }

  /**
   * Find grammar topic with its lessons
   * @param {number} id - Topic ID
   * @returns {Promise<Object>} - Grammar topic with lessons
   */
  async findWithLessons(id) {
    return this.model.findByPk(id, {
      include: ['lessons'],
      where: { isActive: true }
    });
  }
}

export default new GrammarTopicRepository(); 