import { exerciseService } from '../services/index.js';
import BaseController from './base.controller.js';

class ExerciseController extends BaseController {
  constructor() {
    super();
  }

  // Get exercises by lesson ID
  async getExercisesByLesson(req, res) {
    try {
      const { lessonId } = req.params;
      const exercises = await exerciseService.getExercisesByLesson(lessonId);
      
      return res.status(200).json({
        success: true,
        data: exercises
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: 'Error retrieving exercises by lesson',
        error: error.message
      });
    }
  }

  // Get exercises by difficulty
  async getExercisesByDifficulty(req, res) {
    try {
      const { difficulty } = req.params;
      const exercises = await exerciseService.getExercisesByDifficulty(difficulty);
      
      return res.status(200).json({
        success: true,
        data: exercises
      });
    } catch (error) {
      if (error.message.includes('Invalid difficulty')) {
        return res.status(400).json({
          success: false,
          message: error.message
        });
      }
      
      return res.status(500).json({
        success: false,
        message: 'Error retrieving exercises by difficulty',
        error: error.message
      });
    }
  }

  // Validate user's answer for an exercise
  async validateAnswer(req, res) {
    try {
      const { id } = req.params;
      const { answers } = req.body;
      
      if (!answers) {
        return res.status(400).json({
          success: false,
          message: 'Answers are required'
        });
      }
      
      const result = await exerciseService.validateAnswer(id, answers);
      
      return res.status(200).json({
        success: true,
        data: result
      });
    } catch (error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: error.message
        });
      }
      
      return res.status(500).json({
        success: false,
        message: 'Error validating exercise answer',
        error: error.message
      });
    }
  }
}

export default new ExerciseController(); 