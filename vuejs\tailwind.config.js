/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#2196f3',
          dark: '#1976d2',
          light: '#bbdefb',
        },
        secondary: {
          DEFAULT: '#4caf50',
          dark: '#388e3c',
        },
        accent: '#ff9800',
        error: '#f44336',
        warning: '#ff9800',
        success: '#4caf50',
        info: '#2196f3',
      },
      borderRadius: {
        'DEFAULT': '8px',
      },
      boxShadow: {
        'DEFAULT': '0 4px 6px rgba(0, 0, 0, 0.1)',
        'lg': '0 10px 20px rgba(0, 0, 0, 0.12)',
      },
      fontFamily: {
        'sans': ['Roboto', 'Segoe UI', 'Arial', 'sans-serif'],
      },
      backgroundColor: {
        dark: '#121212',
        'dark-card': '#1e1e1e',
      },
      textColor: {
        'dark-primary': '#ffffff',
        'dark-secondary': '#aaaaaa',
      },
    },
  },
  plugins: [],
} 