@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary-color: #2196f3;
  --primary-dark: #1976d2;
  --primary-light: #bbdefb;
  --secondary-color: #4caf50;
  --secondary-dark: #388e3c;
  --accent-color: #ff9800;
  --text-color: #333;
  --text-light: #757575;
  --background-color: #f5f5f5;
  --white: #ffffff;
  --error-color: #f44336;
  --warning-color: #ff9800;
  --success-color: #4caf50;
  --info-color: #2196f3;
  --border-radius: 8px;
  --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

/* Custom styles that will work alongside Tailwind */
@layer components {
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-300;
  }
  
  .btn-primary {
    @apply bg-blue-500 text-white hover:bg-blue-600;
  }
  
  .btn-secondary {
    @apply bg-green-500 text-white hover:bg-green-600;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md p-6 mb-6 transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
  }
  
  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
  }
  
  .badge {
    @apply inline-block px-2 py-1 text-xs font-bold uppercase rounded-full;
  }
}

/* Keep some of the original styles for compatibility */
body {
  font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--background-color);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

h1, h2, h3, h4, h5, h6 {
  margin-bottom: 1rem;
  line-height: 1.2;
  font-weight: 600;
  color: var(--text-color);
}

h1 {
  font-size: 2.5rem;
}

h2 {
  font-size: 2rem;
}

h3 {
  font-size: 1.75rem;
}

p {
  margin-bottom: 1rem;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--primary-dark);
}

/* Form Styles */
.form-group {
  margin-bottom: 1.5rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

input, textarea, select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition);
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-light);
}

button {
  cursor: pointer;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  background-color: var(--primary-color);
  color: var(--white);
  font-size: 1rem;
  font-weight: 500;
  transition: var(--transition);
}

button:hover {
  background-color: var(--primary-dark);
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* Badge Styles */
.badge-primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.badge-secondary {
  background-color: var(--secondary-color);
  color: var(--white);
}

.badge-accent {
  background-color: var(--accent-color);
  color: var(--white);
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.mt-1 {
  margin-top: 0.5rem;
}

.mt-2 {
  margin-top: 1rem;
}

.mt-3 {
  margin-top: 1.5rem;
}

.mt-4 {
  margin-top: 2rem;
}

.mb-1 {
  margin-bottom: 0.5rem;
}

.mb-2 {
  margin-bottom: 1rem;
}

.mb-3 {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 2rem;
}

/* Responsive Styles */
@media (max-width: 768px) {
  h1 {
    font-size: 2rem;
  }
  
  h2 {
    font-size: 1.75rem;
  }
  
  h3 {
    font-size: 1.5rem;
  }
}
