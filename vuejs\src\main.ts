import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'
import { Toast, options } from './plugins/toast'

// Import Font Awesome
import { library } from '@fortawesome/fontawesome-svg-core'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { 
  faChevronLeft, 
  faChevronRight, 
  faSearch, 
  faArrowRight,
  faCheck,
  faTimes,
  faUser,
  faSignOutAlt,
  faBook,
  faSun,
  faMoon
} from '@fortawesome/free-solid-svg-icons'

// Add icons to library
library.add(
  faChevronLeft, 
  faChevronRight, 
  faSearch, 
  faArrowRight,
  faCheck,
  faTimes,
  faUser,
  faSignOutAlt,
  faBook,
  faSun,
  faMoon
)

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(Toast, options)
app.component('font-awesome-icon', FontAwesomeIcon)

app.mount('#app')
