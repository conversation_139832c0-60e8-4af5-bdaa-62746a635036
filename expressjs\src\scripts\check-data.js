import { sequelize } from '../config/database.js';
import * as models from '../models/index.js';
import Exercise from '../models/exercise.model.js';
import Lesson from '../models/lesson.model.js';

async function checkData() {
  try {
    // <PERSON><PERSON><PERSON> b<PERSON><PERSON> kết nối đến database
    await sequelize.authenticate();
    console.log('Database connection established successfully.\n');
    
    // Kiểm tra số lượng bản ghi trong mỗi bảng
    const userCount = await models.User.count();
    const topicCount = await models.GrammarTopic.count();
    const lessonCount = await models.Lesson.count();
    const exerciseCount = await models.Exercise.count();
    
    console.log('=== DATABASE SUMMARY ===');
    console.log(`Users: ${userCount}`);
    console.log(`Grammar Topics: ${topicCount}`);
    console.log(`Lessons: ${lessonCount}`);
    console.log(`Exercises: ${exerciseCount}`);
    console.log('======================\n');
    
    // Lấy dữ liệu users
    console.log('=== USERS ===');
    const users = await models.User.findAll({
      attributes: ['id', 'username', 'email', 'roles']
    });
    users.forEach(user => {
      console.log(`${user.id}. ${user.username} (${user.email}) - Roles: ${user.roles}`);
    });
    
    // Lấy dữ liệu grammar topics
    console.log('\n=== GRAMMAR TOPICS ===');
    const topics = await models.GrammarTopic.findAll();
    topics.forEach(topic => {
      console.log(`${topic.id}. ${topic.name} (Level: ${topic.level}, Order: ${topic.order})`);
    });
    
    // Lấy dữ liệu lessons cho topic đầu tiên
    console.log('\n=== LESSONS FOR TOPIC 1 ===');
    const lessons = await models.Lesson.findAll({ 
      where: { topicId: 1 },
      include: [{
        model: models.GrammarTopic,
        as: 'topic'
      }]
    });
    lessons.forEach(lesson => {
      console.log(`${lesson.id}. ${lesson.title} (Duration: ${lesson.duration} min, Order: ${lesson.order})`);
    });
    
    // Lấy dữ liệu exercises cho lesson đầu tiên
    console.log('\n=== EXERCISES FOR LESSON 1 ===');
    const exercises = await models.Exercise.findAll({ 
      where: { lessonId: 1 },
      include: [{
        model: models.Lesson,
        as: 'lesson'
      }]
    });
    exercises.forEach(exercise => {
      console.log(`${exercise.id}. ${exercise.title} (Type: ${exercise.type}, Difficulty: ${exercise.difficulty}, Points: ${exercise.points})`);
    });
    
    // Kiểm tra số lượng bài tập
    const exercisesCount = await Exercise.findAll();
    console.log(`\nTotal exercises: ${exercisesCount.length}`);
    
    if (exercisesCount.length > 0) {
      console.log('Sample exercise:');
      console.log(JSON.stringify(exercisesCount[0], null, 2));
      
      // Kiểm tra bài tập cho bài học có id = 1
      const lessonExercises = await Exercise.findAll({
        where: { lessonId: 1 }
      });
      console.log(`Exercises for lesson 1: ${lessonExercises.length}`);
      
      if (lessonExercises.length > 0) {
        console.log('Exercises for lesson 1:');
        lessonExercises.forEach(exercise => {
          console.log(`- ${exercise.title} (${exercise.type})`);
        });
      } else {
        console.log('No exercises found for lesson 1');
      }
    } else {
      console.log('No exercises found in database');
    }
    
    // Kiểm tra bài học
    const lessonsCount = await Lesson.findAll();
    console.log(`\nTotal lessons: ${lessonsCount.length}`);
    
    if (lessonsCount.length > 0) {
      console.log('First lesson:');
      console.log(`- ID: ${lessonsCount[0].id}`);
      console.log(`- Title: ${lessonsCount[0].title}`);
      console.log(`- TopicId: ${lessonsCount[0].topicId}`);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error checking data:', error);
    process.exit(1);
  }
}

// Chạy function kiểm tra
checkData(); 