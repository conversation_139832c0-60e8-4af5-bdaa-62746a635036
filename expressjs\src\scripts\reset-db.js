import fs from 'fs';
import path from 'path';
import { sequelize } from '../config/database.js';

async function resetDatabase() {
  try {
    console.log('Starting database reset...');
    
    // <PERSON><PERSON><PERSON> đ<PERSON>nh đ<PERSON>ờng dẫn file database
    const dbPath = sequelize.options.storage;
    console.log(`Database path: ${dbPath}`);
    
    // <PERSON><PERSON>g kết nối hiện tại
    await sequelize.close();
    console.log('Database connection closed.');
    
    // Xóa file database cũ nếu tồn tại
    if (fs.existsSync(dbPath)) {
      console.log(`Removing existing database file: ${dbPath}`);
      fs.unlinkSync(dbPath);
      console.log('Database file removed successfully.');
    } else {
      console.log('No existing database file found.');
    }
    
    // Tạo file database mới (rỗng)
    fs.writeFileSync(dbPath, '');
    console.log('Created empty database file.');
    
    console.log('Database reset completed successfully!');
    console.log('Please run "npm run seed" to populate the database with sample data.');
    process.exit(0);
  } catch (error) {
    console.error('Error resetting database:', error);
    process.exit(1);
  }
}

// Chạy function reset
resetDatabase(); 