import { sequelize } from '../config/database.js';
import User from '../models/user.model.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get directory name for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Backup database file before making changes
const backupDatabase = () => {
  const dbPath = process.env.DB_STORAGE || './database.sqlite';
  const backupPath = `${dbPath}.backup-${Date.now()}`;
  
  try {
    if (fs.existsSync(dbPath)) {
      fs.copyFileSync(dbPath, backupPath);
      console.log(`Database backup created at ${backupPath}`);
    }
  } catch (error) {
    console.error('Failed to create database backup:', error);
    process.exit(1);
  }
};

// Initialize database
const initDatabase = async () => {
  try {
    console.log('Checking database connection...');
    await sequelize.authenticate();
    console.log('Database connection established successfully.');
    
    // Create backup before sync
    backupDatabase();
    
    console.log('Synchronizing database models...');
    // Use force: false to prevent data loss
    await sequelize.sync({ force: false });
    
    console.log('Database synchronized successfully.');
    
    // Check if admin user exists, create if not
    const adminCount = await User.count({
      where: {
        username: 'admin'
      }
    });
    
    if (adminCount === 0) {
      console.log('Creating admin user...');
      await User.create({
        username: 'admin',
        email: '<EMAIL>',
        password: '$2a$10$XOPbrlUPQdwdJUpSrIF6X.LbE14qsMmKGhM1A8W9iqaG3vv1BD7WC', // 'admin123'
        roles: 'admin',
        isActive: true
      });
      console.log('Admin user created successfully.');
    } else {
      console.log('Admin user already exists.');
    }
    
    console.log('Database initialization completed.');
    process.exit(0);
  } catch (error) {
    console.error('Database initialization failed:', error);
    process.exit(1);
  }
};

// Run initialization
initDatabase(); 