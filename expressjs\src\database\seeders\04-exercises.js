export async function seed(db) {
  try {
    // Lấy tất cả lessons và topics
    const lessons = await db.Lesson.findAll();
    const topics = await db.GrammarTopic.findAll();
    
    // Tạo map của topics theo id
    const topicMap = {};
    topics.forEach(topic => {
      topicMap[topic.id] = topic.name;
    });
    
    // Tạo map của lessons theo id và topic
    const lessonMap = {};
    lessons.forEach(lesson => {
      const topicName = topicMap[lesson.topicId];
      const key = `${topicName} - ${lesson.title}`;
      lessonMap[key] = lesson.id;
    });
    
    // Tạo dữ liệu mẫu
    await db.Exercise.bulkCreate([
      // Present Simple exercises
      {
        lessonId: lessonMap['Present Simple Tense - Introduction to Present Simple'],
        title: 'Present Simple Practice 1',
        type: 'multiple-choice',
        instructions: 'Choose the correct form of the verb in present simple tense.',
        content: JSON.stringify([
          {
            question: 'She _____ in a bank.',
            options: ['work', 'works', 'working', 'is working']
          },
          {
            question: 'They _____ football every weekend.',
            options: ['play', 'plays', 'playing', 'are playing']
          },
          {
            question: 'I _____ coffee in the morning.',
            options: ['drink', 'drinks', 'drinking', 'am drinking']
          },
          {
            question: 'He _____ English very well.',
            options: ['speak', 'speaks', 'speaking', 'is speaking']
          },
          {
            question: 'We _____ to the cinema once a month.',
            options: ['go', 'goes', 'going', 'are going']
          }
        ]),
        answers: JSON.stringify(['works', 'play', 'drink', 'speaks', 'go']),
        difficulty: 'easy',
        points: 10,
        order: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        lessonId: lessonMap['Present Simple Tense - Introduction to Present Simple'],
        title: 'Present Simple Practice 2',
        type: 'fill-in-blanks',
        instructions: 'Fill in the blanks with the correct form of the verbs in brackets.',
        content: JSON.stringify({
          text: `1. John (live) _____ in London.
2. They (not/work) _____ on Sundays.
3. She (study) _____ English every day.
4. We (not/like) _____ coffee.
5. (you/play) _____ any musical instruments?`,
          options: [
            ['live', 'lives', 'is living', 'living'],
            ['not work', 'don\'t work', 'doesn\'t work', 'aren\'t working'],
            ['study', 'studies', 'is studying', 'studying'],
            ['not like', 'don\'t like', 'doesn\'t like', 'aren\'t liking'],
            ['You play', 'Do you play', 'Are you playing', 'Does you play']
          ]
        }),
        answers: JSON.stringify(['lives', 'don\'t work', 'studies', 'don\'t like', 'Do you play']),
        difficulty: 'medium',
        points: 15,
        order: 2,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      
      // Present Continuous exercises
      {
        lessonId: lessonMap['Present Continuous Tense - Introduction to Present Continuous'],
        title: 'Present Continuous Practice',
        type: 'multiple-choice',
        instructions: 'Choose the correct form of the verb in present continuous tense.',
        content: JSON.stringify([
          {
            question: 'Look! It _____ outside.',
            options: ['rain', 'rains', 'raining', 'is raining']
          },
          {
            question: 'They _____ dinner right now.',
            options: ['have', 'has', 'having', 'are having']
          },
          {
            question: 'I _____ to music at the moment.',
            options: ['listen', 'listens', 'listening', 'am listening']
          },
          {
            question: 'She _____ a book now.',
            options: ['read', 'reads', 'reading', 'is reading']
          },
          {
            question: 'We _____ for the bus.',
            options: ['wait', 'waits', 'waiting', 'are waiting']
          }
        ]),
        answers: JSON.stringify(['is raining', 'are having', 'am listening', 'is reading', 'are waiting']),
        difficulty: 'easy',
        points: 10,
        order: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      
      // Past Simple exercises
      {
        lessonId: lessonMap['Past Simple Tense - Introduction to Past Simple'],
        title: 'Past Simple Practice',
        type: 'multiple-choice',
        instructions: 'Choose the correct form of the verb in past simple tense.',
        content: JSON.stringify([
          {
            question: 'I _____ to the cinema yesterday.',
            options: ['go', 'went', 'gone', 'going']
          },
          {
            question: 'She _____ her homework last night.',
            options: ['do', 'did', 'done', 'doing']
          },
          {
            question: 'They _____ breakfast at 8 AM.',
            options: ['have', 'had', 'having', 'has']
          },
          {
            question: 'We _____ to the party.',
            options: ['not go', 'not went', 'didn\'t go', 'weren\'t go']
          },
          {
            question: '_____ you _____ the movie?',
            options: ['Did, like', 'Do, like', 'Did, liked', 'Were, liking']
          }
        ]),
        answers: JSON.stringify(['went', 'did', 'had', 'didn\'t go', 'Did, like']),
        difficulty: 'medium',
        points: 15,
        order: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      
      // Past Continuous exercises
      {
        lessonId: lessonMap['Past Continuous Tense - Introduction to Past Continuous'],
        title: 'Past Continuous Practice',
        type: 'fill-in-blanks',
        instructions: 'Fill in the blanks with the correct form of the verbs in brackets using the past continuous tense.',
        content: JSON.stringify({
          text: `1. I (sleep) _____ when the phone rang.
2. They (play) _____ tennis when it started to rain.
3. She (not/work) _____ at 8 PM last night.
4. What (you/do) _____ when I called?
5. The children (watch) _____ TV when their parents came home.`,
          options: [
            ['slept', 'was sleeping', 'sleep', 'am sleeping'],
            ['played', 'were playing', 'play', 'are playing'],
            ['not working', 'wasn\'t working', 'didn\'t work', 'doesn\'t work'],
            ['you did', 'were you doing', 'you do', 'are you doing'],
            ['watched', 'were watching', 'watch', 'are watching']
          ]
        }),
        answers: JSON.stringify(['was sleeping', 'were playing', 'wasn\'t working', 'were you doing', 'were watching']),
        difficulty: 'medium',
        points: 15,
        order: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      
      // Present Perfect exercises
      {
        lessonId: lessonMap['Present Perfect Tense - Introduction to Present Perfect'],
        title: 'Present Perfect Practice',
        type: 'multiple-choice',
        instructions: 'Choose the correct form of the verb in present perfect tense.',
        content: JSON.stringify([
          {
            question: 'I _____ to Paris three times.',
            options: ['go', 'went', 'gone', 'have been']
          },
          {
            question: 'She _____ her keys.',
            options: ['lose', 'lost', 'has lost', 'is losing']
          },
          {
            question: 'They _____ here since 2010.',
            options: ['are', 'were', 'have been', 'had been']
          },
          {
            question: 'We _____ that movie yet.',
            options: ['don\'t see', 'didn\'t see', 'haven\'t seen', 'aren\'t seeing']
          },
          {
            question: '_____ you ever _____ to Japan?',
            options: ['Have, been', 'Did, go', 'Have, gone', 'Do, go']
          }
        ]),
        answers: JSON.stringify(['have been', 'has lost', 'have been', 'haven\'t seen', 'Have, been']),
        difficulty: 'hard',
        points: 20,
        order: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      
      // Conditionals exercises
      {
        lessonId: lessonMap['Conditional Sentences - Introduction to Conditionals'],
        title: 'Conditionals Practice',
        type: 'matching',
        instructions: 'Match the first part of the conditional sentence with the correct second part.',
        content: JSON.stringify({
          left: [
            'If it rains tomorrow,',
            'If I won the lottery,',
            'If you heat water to 100°C,',
            'If I had studied harder,',
            'If I were you,'
          ],
          right: [
            'I would buy a big house.',
            'I would have passed the exam.',
            'I would take that job.',
            'it boils.',
            'I will stay at home.'
          ]
        }),
        answers: JSON.stringify([4, 0, 3, 1, 2]),
        difficulty: 'hard',
        points: 20,
        order: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      
      // Additional Conditionals exercise
      {
        lessonId: lessonMap['Conditional Sentences - Introduction to Conditionals'],
        title: 'Conditional Types Practice',
        type: 'multiple-choice',
        instructions: 'Identify the type of conditional sentence.',
        content: JSON.stringify([
          {
            question: 'If it rains, the ground gets wet.',
            options: ['Zero conditional', 'First conditional', 'Second conditional', 'Third conditional']
          },
          {
            question: 'If I win the lottery, I will buy a new house.',
            options: ['Zero conditional', 'First conditional', 'Second conditional', 'Third conditional']
          },
          {
            question: 'If I were rich, I would travel around the world.',
            options: ['Zero conditional', 'First conditional', 'Second conditional', 'Third conditional']
          },
          {
            question: 'If she had studied harder, she would have passed the exam.',
            options: ['Zero conditional', 'First conditional', 'Second conditional', 'Third conditional']
          },
          {
            question: 'If you mix blue and yellow, you get green.',
            options: ['Zero conditional', 'First conditional', 'Second conditional', 'Third conditional']
          }
        ]),
        answers: JSON.stringify(['Zero conditional', 'First conditional', 'Second conditional', 'Third conditional', 'Zero conditional']),
        difficulty: 'medium',
        points: 15,
        order: 2,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      
      // Reported Speech exercises
      {
        lessonId: lessonMap['Reported Speech - Introduction to Reported Speech'],
        title: 'Reported Speech Practice',
        type: 'fill-in-blanks',
        instructions: 'Rewrite the direct speech as reported speech.',
        content: JSON.stringify({
          text: `1. "I am happy," she said. → She said that she _____ happy.
2. "I will call you tomorrow," he promised. → He promised that he _____ call me the next day.
3. "I don't like coffee," Tom said. → Tom said that he _____ like coffee.
4. "I have finished my homework," Mary said. → Mary said that she _____ her homework.
5. "We are moving to London," they announced. → They announced that they _____ to London.`,
          options: [
            ['am', 'is', 'was', 'were'],
            ['will', 'would', 'had', 'could'],
            ['don\'t', 'didn\'t', 'doesn\'t', 'isn\'t'],
            ['has finished', 'had finished', 'finished', 'was finishing'],
            ['are moving', 'were moving', 'moved', 'had moved']
          ]
        }),
        answers: JSON.stringify(['was', 'would', 'didn\'t', 'had finished', 'were moving']),
        difficulty: 'hard',
        points: 25,
        order: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      
      // Additional Reported Speech exercise
      {
        lessonId: lessonMap['Reported Speech - Introduction to Reported Speech'],
        title: 'Reported Speech Questions Practice',
        type: 'multiple-choice',
        instructions: 'Choose the correct reported speech form for each direct question.',
        content: JSON.stringify([
          {
            question: '"Where do you live?" she asked me.',
            options: [
              'She asked me where do I live.',
              'She asked me where I lived.',
              'She asked me where I live.',
              'She asked me where did I live.'
            ]
          },
          {
            question: '"Are you enjoying the party?" he asked.',
            options: [
              'He asked if I am enjoying the party.',
              'He asked if I was enjoying the party.',
              'He asked if was I enjoying the party.',
              'He asked if I enjoyed the party.'
            ]
          },
          {
            question: '"Have you finished your homework?" the teacher asked.',
            options: [
              'The teacher asked if I have finished my homework.',
              'The teacher asked if I had finished my homework.',
              'The teacher asked if had I finished my homework.',
              'The teacher asked if I finished my homework.'
            ]
          },
          {
            question: '"Will you help me tomorrow?" she asked.',
            options: [
              'She asked if I will help her the next day.',
              'She asked if I would help her the next day.',
              'She asked if would I help her the next day.',
              'She asked if I will help her tomorrow.'
            ]
          },
          {
            question: '"What time did you arrive?" he asked.',
            options: [
              'He asked what time I arrived.',
              'He asked what time did I arrive.',
              'He asked what time I had arrived.',
              'He asked what time had I arrived.'
            ]
          }
        ]),
        answers: JSON.stringify([
          'She asked me where I lived.',
          'He asked if I was enjoying the party.',
          'The teacher asked if I had finished my homework.',
          'She asked if I would help her the next day.',
          'He asked what time I had arrived.'
        ]),
        difficulty: 'hard',
        points: 20,
        order: 2,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]);
    
    console.log('Exercises seeded successfully');
  } catch (error) {
    console.error('Error seeding exercises:', error);
    throw error;
  }
} 