export async function seed(db) {
  // Tạo dữ liệu mẫu
  await db.GrammarTopic.bulkCreate([
    {
      name: 'Present Simple Tense',
      description: 'The present simple tense is used to describe habits, unchanging situations, general truths, and fixed arrangements.',
      level: 'beginner',
      order: 1,
      imageUrl: 'https://example.com/images/present-simple.jpg',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      name: 'Present Continuous Tense',
      description: 'The present continuous tense is used to describe actions that are happening now or temporary actions.',
      level: 'beginner',
      order: 2,
      imageUrl: 'https://example.com/images/present-continuous.jpg',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      name: 'Past Simple Tense',
      description: 'The past simple tense is used to describe actions that were completed in the past.',
      level: 'beginner',
      order: 3,
      imageUrl: 'https://example.com/images/past-simple.jpg',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      name: 'Past Continuous Tense',
      description: 'The past continuous tense is used to describe actions that were in progress at a specific time in the past.',
      level: 'intermediate',
      order: 1,
      imageUrl: 'https://example.com/images/past-continuous.jpg',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      name: 'Present Perfect Tense',
      description: 'The present perfect tense is used to describe actions that started in the past and continue to the present or have an effect on the present.',
      level: 'intermediate',
      order: 2,
      imageUrl: 'https://example.com/images/present-perfect.jpg',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      name: 'Conditional Sentences',
      description: 'Conditional sentences are used to express that the action in the main clause can only take place if a certain condition is fulfilled.',
      level: 'advanced',
      order: 1,
      imageUrl: 'https://example.com/images/conditionals.jpg',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      name: 'Reported Speech',
      description: 'Reported speech is used to convey what someone else said without quoting them directly.',
      level: 'advanced',
      order: 2,
      imageUrl: 'https://example.com/images/reported-speech.jpg',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ]);
  
  console.log('Grammar topics seeded successfully');
} 