<template>
  <footer class="bg-gray-800 text-white pt-12 pb-6 mt-8">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex flex-wrap justify-between mb-8">
        <div class="w-full md:w-1/3 mb-8 md:mb-0">
          <h3 class="text-xl font-bold mb-4 text-primary">English Grammar Learning</h3>
          <p class="text-gray-300">Improve your English grammar skills with our interactive lessons and exercises.</p>
        </div>
        
        <div class="flex flex-wrap md:w-2/3 gap-12">
          <div class="w-1/2 sm:w-auto">
            <h4 class="text-lg font-bold mb-4 text-primary">Quick Links</h4>
            <ul>
              <li class="mb-2"><router-link to="/" class="text-gray-300 hover:text-primary transition-colors duration-200">Home</router-link></li>
              <li class="mb-2"><router-link to="/topics" class="text-gray-300 hover:text-primary transition-colors duration-200">Topics</router-link></li>
              <li class="mb-2"><router-link to="/topics/beginner" class="text-gray-300 hover:text-primary transition-colors duration-200">Beginner</router-link></li>
              <li class="mb-2"><router-link to="/topics/intermediate" class="text-gray-300 hover:text-primary transition-colors duration-200">Intermediate</router-link></li>
              <li class="mb-2"><router-link to="/topics/advanced" class="text-gray-300 hover:text-primary transition-colors duration-200">Advanced</router-link></li>
            </ul>
          </div>
          
          <div class="w-1/2 sm:w-auto">
            <h4 class="text-lg font-bold mb-4 text-primary">Account</h4>
            <ul>
              <li v-if="isAuthenticated" class="mb-2"><router-link to="/profile" class="text-gray-300 hover:text-primary transition-colors duration-200">Profile</router-link></li>
              <li v-if="isAuthenticated" class="mb-2"><router-link to="/progress" class="text-gray-300 hover:text-primary transition-colors duration-200">My Progress</router-link></li>
              <li v-if="!isAuthenticated" class="mb-2"><router-link to="/login" class="text-gray-300 hover:text-primary transition-colors duration-200">Login</router-link></li>
              <li v-if="!isAuthenticated" class="mb-2"><router-link to="/register" class="text-gray-300 hover:text-primary transition-colors duration-200">Register</router-link></li>
            </ul>
          </div>
        </div>
      </div>
      
      <div class="border-t border-gray-600 pt-6 text-center text-gray-400 text-sm">
        <p>&copy; {{ currentYear }} English Grammar Learning. All rights reserved.</p>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { authService } from '../../services/auth.service';

defineOptions({
  name: 'FooterComponent'
});

const isAuthenticated = computed(() => {
  return authService.isAuthenticated();
});

const currentYear = ref(new Date().getFullYear());
</script> 