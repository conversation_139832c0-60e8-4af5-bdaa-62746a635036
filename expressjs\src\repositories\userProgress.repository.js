import BaseRepository from './base.repository.js';
import { UserProgress } from '../models/index.js';

class UserProgressRepository extends BaseRepository {
  constructor() {
    super(UserProgress);
  }

  /**
   * Find or create user progress for a lesson
   * @param {number} userId - User ID
   * @param {number} lessonId - Lesson ID
   * @param {Object} defaults - Default values if creating new record
   * @returns {Promise<Array>} - [userProgress, created]
   */
  async findOrCreateLessonProgress(userId, lessonId, defaults = {}) {
    return this.model.findOrCreate({
      where: {
        userId,
        lessonId
      },
      defaults
    });
  }

  /**
   * Find or create user progress for an exercise
   * @param {number} userId - User ID
   * @param {number} exerciseId - Exercise ID
   * @param {Object} defaults - Default values if creating new record
   * @returns {Promise<Array>} - [userProgress, created]
   */
  async findOrCreateExerciseProgress(userId, exerciseId, defaults = {}) {
    return this.model.findOrCreate({
      where: {
        userId,
        exerciseId
      },
      defaults
    });
  }

  /**
   * Find all user progress for a specific user
   * @param {number} userId - User ID
   * @returns {Promise<Array>} - Array of user progress records
   */
  async findByUserId(userId) {
    return this.model.findAll({
      where: { userId },
      include: ['lesson', 'exercise']
    });
  }
}

export default new UserProgressRepository(); 