<template>
  <div class="max-w-3xl mx-auto px-4 py-8">
    <div v-if="isLoading" class="text-center py-8 text-gray-600 dark:text-gray-400">
      Loading topic and lessons...
    </div>
    
    <div v-else-if="error" class="text-center p-4 text-red-600 dark:text-red-400">
      {{ error }}
      <button @click="fetchTopicWithLessons" class="mt-4 px-4 py-2 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors dark:text-gray-200">Retry</button>
    </div>
    
    <div v-else-if="topic">
      <div class="flex justify-between items-center mb-4">
        <router-link to="/topics" class="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
          &larr; Back to Topics
        </router-link>
        <div 
          class="px-2 py-1 rounded text-xs font-bold uppercase text-white"
          :class="{
            'bg-green-500': topic.level === 'beginner',
            'bg-blue-500': topic.level === 'intermediate',
            'bg-purple-500': topic.level === 'advanced'
          }"
        >
          {{ topic.level }}
        </div>
      </div>
      
      <h1 class="text-3xl font-bold mb-2 text-gray-900 dark:text-white">{{ topic.title }}</h1>
      <p class="text-lg text-gray-700 dark:text-gray-300 mb-8 leading-relaxed">{{ topic.description }}</p>
      
      <div v-if="topic.lessons && topic.lessons.length > 0" class="mt-8">
        <h2 class="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Lessons</h2>
        <div v-for="lesson in sortedLessons" :key="lesson.id" class="p-6 mb-6 rounded-lg shadow-md bg-white dark:bg-dark-card">
          <h3 class="text-xl font-bold mb-4 text-gray-800 dark:text-gray-200">{{ lesson.title }}</h3>
          <div class="flex flex-wrap gap-4 mt-4">
            <router-link :to="`/lessons/${lesson.id}`" class="px-4 py-2 bg-blue-500 text-white font-medium rounded-md hover:bg-blue-600 transition-colors">
              View Lesson
            </router-link>
            <router-link :to="`/lessons/${lesson.id}/exercises`" class="px-4 py-2 bg-green-500 text-white font-medium rounded-md hover:bg-green-600 transition-colors">
              Practice Exercises
            </router-link>
          </div>
        </div>
      </div>
      
      <div v-else class="text-center py-8 bg-gray-50 dark:bg-gray-800 rounded-lg text-gray-600 dark:text-gray-400">
        No lessons available for this topic yet.
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import { grammarService } from '../../services/grammar.service';

const route = useRoute();

interface Lesson {
  id: string;
  title: string;
  content: string;
  topicId: string;
  order: number;
}

interface GrammarTopic {
  id: string;
  title: string;
  description: string;
  level: string;
  imageUrl?: string;
  lessons?: Lesson[];
}

const topic = ref<GrammarTopic | null>(null);
const isLoading = ref(true);
const error = ref('');

const topicId = computed(() => {
  return route.params.id as string;
});

const sortedLessons = computed(() => {
  if (!topic.value?.lessons) return [];
  return [...topic.value.lessons].sort((a, b) => a.order - b.order);
});

const fetchTopicWithLessons = async () => {
  error.value = '';
  isLoading.value = true;
  
  try {
    if (!topicId.value) {
      error.value = 'Invalid topic ID. Please go back to topics list and try again.';
      isLoading.value = false;
      return;
    }
    
    topic.value = await grammarService.getTopicWithLessons(topicId.value);
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to load topic and lessons.';
  } finally {
    isLoading.value = false;
  }
};

onMounted(fetchTopicWithLessons);
</script> 