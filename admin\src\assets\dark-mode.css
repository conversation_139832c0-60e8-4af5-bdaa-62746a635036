/* Dark Mode Styles */
:root {
  --bg-light: #f3f4f6;
  --text-light: #111827;
  --card-bg-light: #ffffff;
  --border-light: #d1d5db;
  
  --bg-dark: #000000;
  --text-dark: #f9fafb;
  --card-bg-dark: #111827;
  --border-dark: #374151;
  
  --primary-color: #6d28d9;
  --primary-hover: #5b21b6;
  --primary-dark-hover: #7c3aed;
}

/* Apply dark mode when .dark-mode class is present */
.dark-mode {
  background-color: var(--bg-dark);
  color: var(--text-dark);
}

/* Card styles */
.dark-mode .card,
.dark-mode .panel {
  background-color: var(--card-bg-dark);
  border-color: var(--border-dark);
}

/* Form elements */
.dark-mode input,
.dark-mode select,
.dark-mode textarea {
  background-color: #1f2937;
  color: var(--text-dark);
  border-color: var(--border-dark);
}

.dark-mode input:focus,
.dark-mode select:focus,
.dark-mode textarea:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(109, 40, 217, 0.3);
}

/* Tables */
.dark-mode table {
  color: var(--text-dark);
}

.dark-mode th {
  background-color: #1f2937;
}

.dark-mode tr:nth-child(even) {
  background-color: #1f2937;
}

.dark-mode tr:nth-child(odd) {
  background-color: #111827;
}

.dark-mode tr:hover {
  background-color: #374151;
}

/* Buttons */
.dark-mode button:not(.theme-toggle):not(.primary-button) {
  background-color: #374151;
  color: var(--text-dark);
  border-color: var(--border-dark);
}

.dark-mode button:not(.theme-toggle):not(.primary-button):hover {
  background-color: #4b5563;
}

/* Links */
.dark-mode a {
  color: #93c5fd;
}

.dark-mode a:hover {
  color: #bfdbfe;
}

/* Transitions for smooth theme switching */
body,
input,
select,
textarea,
button,
a,
.card,
.panel {
  transition: background-color 0.3s, color 0.3s, border-color 0.3s, box-shadow 0.3s;
} 