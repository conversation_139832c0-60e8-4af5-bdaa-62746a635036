<template>
  <div class="max-w-6xl mx-auto px-4 py-8">
    <div class="text-center mb-12">
      <h1 class="text-4xl font-extrabold mb-4 text-gray-900 dark:text-white">Grammar <span class="text-blue-500 dark:text-blue-400">Topics</span></h1>
      <p class="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">Explore our comprehensive collection of grammar lessons</p>
    </div>
    
    <div v-if="!isAuthenticated" class="mb-8 p-4 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg text-blue-600 dark:text-blue-300">
      <div class="flex items-center">
        <span class="mr-3 text-xl">ℹ️</span>
        <div>
          <p class="font-medium">You're browsing as a guest</p>
          <p class="text-sm mt-1">Please <router-link to="/login" class="font-bold underline">login</router-link> or <router-link to="/register" class="font-bold underline">register</router-link> to access lessons and exercises.</p>
        </div>
      </div>
    </div>
    
    <div class="flex flex-col md:flex-row justify-between items-center mb-8 gap-4">
      <div>
        <h3 class="mb-4 text-lg font-medium text-gray-800 dark:text-gray-200">Filter by Level</h3>
        <div class="flex flex-wrap gap-3">
          <router-link to="/topics" class="flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg transition-all duration-300 hover:bg-gray-200 dark:hover:bg-gray-600" :class="{ 'bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-300 border border-blue-200 dark:border-blue-700': !currentLevel }">
            <span class="flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="8" y1="12" x2="16" y2="12"></line>
                <line x1="8" y1="8" x2="16" y2="8"></line>
                <line x1="8" y1="16" x2="16" y2="16"></line>
              </svg>
            </span>
            All Levels
          </router-link>
          <router-link to="/topics/beginner" class="flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg transition-all duration-300 hover:bg-gray-200 dark:hover:bg-gray-600" :class="{ 'bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-300 border border-blue-200 dark:border-blue-700': currentLevel === 'beginner' }">
            <span class="flex items-center justify-center text-green-500 dark:text-green-400">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
              </svg>
            </span>
            Beginner
          </router-link>
          <router-link to="/topics/intermediate" class="flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg transition-all duration-300 hover:bg-gray-200 dark:hover:bg-gray-600" :class="{ 'bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-300 border border-blue-200 dark:border-blue-700': currentLevel === 'intermediate' }">
            <span class="flex items-center justify-center text-blue-500 dark:text-blue-400">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M2 12h20"></path>
              </svg>
            </span>
            Intermediate
          </router-link>
          <router-link to="/topics/advanced" class="flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg transition-all duration-300 hover:bg-gray-200 dark:hover:bg-gray-600" :class="{ 'bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-300 border border-blue-200 dark:border-blue-700': currentLevel === 'advanced' }">
            <span class="flex items-center justify-center text-purple-500 dark:text-purple-400">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22 4 12 14.01 9 11.01"></polyline>
              </svg>
            </span>
            Advanced
          </router-link>
        </div>
      </div>
      
      <div class="relative w-full md:w-72">
        <input 
          type="text" 
          placeholder="Search topics..." 
          v-model="searchTerm" 
          @input="filterTopics"
          class="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
        >
        <span class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 dark:text-gray-500">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="11" cy="11" r="8"></circle>
            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
          </svg>
        </span>
      </div>
    </div>
    
    <div v-if="isLoading" class="flex flex-col items-center justify-center py-16 text-gray-600 dark:text-gray-300">
      <div class="w-10 h-10 border-4 border-blue-200 dark:border-blue-800 border-t-blue-500 dark:border-t-blue-400 rounded-full animate-spin mb-4"></div>
      <p>Loading topics...</p>
    </div>
    
    <div v-else-if="error" class="text-center py-12 max-w-lg mx-auto">
      <div class="text-red-500 dark:text-red-400 mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mx-auto">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="12" y1="8" x2="12" y2="12"></line>
          <line x1="12" y1="16" x2="12.01" y2="16"></line>
        </svg>
      </div>
      <h3 class="text-xl font-bold mb-2 dark:text-gray-200">Oops! Something went wrong</h3>
      <p class="text-gray-600 dark:text-gray-400 mb-6">{{ error }}</p>
      <button @click="fetchTopics" class="px-6 py-3 bg-gradient-to-r from-blue-600 to-cyan-400 text-white font-semibold rounded-lg transition-all duration-300 hover:shadow-lg hover:-translate-y-1">Try Again</button>
    </div>
    
    <div v-else-if="filteredTopics.length === 0" class="text-center py-16 text-gray-600 dark:text-gray-400">
      <div class="text-gray-300 dark:text-gray-600 mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="mx-auto">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
          <polyline points="14 2 14 8 20 8"></polyline>
          <line x1="12" y1="18" x2="12" y2="12"></line>
          <line x1="9" y1="15" x2="15" y2="15"></line>
        </svg>
      </div>
      <h3 class="text-xl font-bold mb-2 dark:text-gray-200">No topics found</h3>
      <p v-if="searchTerm" class="dark:text-gray-400">Try adjusting your search terms</p>
      <p v-else class="dark:text-gray-400">No topics available for the selected level</p>
    </div>
    
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div v-for="topic in filteredTopics" :key="topic.id" class="flex flex-col rounded-2xl overflow-hidden bg-white dark:bg-dark-card shadow-md transition-all duration-300 hover:-translate-y-2 hover:shadow-xl">
        <div class="h-40 flex items-center justify-center overflow-hidden relative"
          :class="{
            'bg-gradient-to-r from-green-400 to-green-600': topic.level === 'beginner',
            'bg-gradient-to-r from-blue-400 to-blue-600': topic.level === 'intermediate',
            'bg-gradient-to-r from-purple-400 to-purple-600': topic.level === 'advanced'
          }"
        >
          <div 
            v-if="topic.imageUrl" 
            :style="{ backgroundImage: `url(${topic.imageUrl})` }"
            class="w-full h-full bg-cover bg-center"
          ></div>
          <div v-else-if="topic.level === 'beginner'" class="absolute inset-0 flex items-center justify-center">
            <div class="text-white text-center">
              <div class="text-3xl font-bold mb-1">BEGINNER</div>
            </div>
          </div>
          <div v-else-if="topic.level === 'intermediate'" class="absolute inset-0 flex items-center justify-center">
            <div class="text-white text-center">
              <div class="text-3xl font-bold mb-1">INTERMEDIATE</div>
            </div>
          </div>
          <div v-else-if="topic.level === 'advanced'" class="absolute inset-0 flex items-center justify-center">
            <div class="text-white text-center">
              <div class="text-3xl font-bold mb-1">ADVANCED</div>
            </div>
          </div>
          <div v-else class="absolute inset-0 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
              <line x1="16" y1="13" x2="8" y2="13"></line>
              <line x1="16" y1="17" x2="8" y2="17"></line>
              <polyline points="10 9 9 9 8 9"></polyline>
            </svg>
          </div>
        </div>
        
        <div class="p-6 flex-grow flex flex-col">
          <span class="inline-block px-3 py-1 rounded-full text-xs font-bold uppercase mb-3"
            :class="{
              'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300': topic.level === 'beginner',
              'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300': topic.level === 'intermediate',
              'bg-purple-100 text-purple-600 dark:bg-purple-900 dark:text-purple-300': topic.level === 'advanced'
            }"
          >
            {{ topic.level }}
          </span>
          <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-gray-200">{{ topic.title }}</h3>
          <p class="text-gray-600 dark:text-gray-400 leading-relaxed mb-6 flex-grow">{{ topic.description }}</p>
          <router-link :to="topic.id ? `/topics/${topic.id}/lessons` : '/topics'" class="flex items-center justify-between px-5 py-3 bg-gradient-to-r from-blue-600 to-cyan-400 text-white rounded-lg font-medium transition-all duration-300 hover:shadow-md mt-auto group">
            View Lessons
            <font-awesome-icon icon="chevron-right" class="transition-transform duration-300 group-hover:translate-x-1" />
          </router-link>
        </div>
      </div>
    </div>
    
    <div class="flex items-center justify-center mt-12 gap-4" v-if="totalPages > 1">
      <button 
        @click="changePage(currentPage - 1)" 
        :disabled="currentPage === 1"
        class="w-10 h-10 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100 dark:hover:bg-gray-700"
      >
        <font-awesome-icon icon="chevron-left" class="text-gray-600 dark:text-gray-400" />
      </button>
      
      <span class="font-medium text-gray-700 dark:text-gray-300">{{ currentPage }} / {{ totalPages }}</span>
      
      <button 
        @click="changePage(currentPage + 1)" 
        :disabled="currentPage === totalPages"
        class="w-10 h-10 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100 dark:hover:bg-gray-700"
      >
        <font-awesome-icon icon="chevron-right" class="text-gray-600 dark:text-gray-400" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import { grammarService } from '../../services/grammar.service';
import { authService } from '../../services/auth.service';

const route = useRoute();

interface GrammarTopic {
  id: string;
  title: string;
  description: string;
  level: string;
  imageUrl?: string;
}

const topics = ref<GrammarTopic[]>([]);
const filteredTopics = ref<GrammarTopic[]>([]);
const isLoading = ref(true);
const error = ref('');
const searchTerm = ref('');

// Authentication status
const isAuthenticated = computed(() => {
  return authService.isAuthenticated();
});

// Pagination
const itemsPerPage = 6;
const currentPage = ref(1);
const totalPages = computed(() => {
  return Math.ceil(filteredTopics.value.length / itemsPerPage);
});

const currentLevel = computed(() => {
  return route.params.level as string || '';
});

const fetchTopics = async () => {
  error.value = '';
  isLoading.value = true;
  
  try {
    if (currentLevel.value) {
      topics.value = await grammarService.getTopicsByLevel(currentLevel.value);
    } else {
      topics.value = await grammarService.getAllTopics();
    }
    filterTopics();
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to load grammar topics.';
    filteredTopics.value = [];
  } finally {
    isLoading.value = false;
  }
};

const filterTopics = () => {
  if (!searchTerm.value) {
    filteredTopics.value = [...topics.value];
  } else {
    const term = searchTerm.value.toLowerCase();
    filteredTopics.value = topics.value.filter(topic => 
      topic.title.toLowerCase().includes(term) || 
      topic.description.toLowerCase().includes(term)
    );
  }
  currentPage.value = 1; // Reset to first page when filtering
};

const changePage = (page: number) => {
  currentPage.value = page;
};

// Watch for route changes to refetch topics
watch(
  () => route.params.level,
  () => fetchTopics()
);

onMounted(fetchTopics);
</script> 