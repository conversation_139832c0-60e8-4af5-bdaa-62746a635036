import { DataTypes, Model } from 'sequelize';
import { sequelize } from '../config/database.js';

class Achievement extends Model {}

Achievement.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  type: {
    type: DataTypes.ENUM('lesson_completion', 'quiz_score', 'streak', 'exercise_mastery', 'topic_completion'),
    allowNull: false
  },
  requirement: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: 'JSON string containing achievement requirements'
  },
  points: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 10
  },
  badgeUrl: {
    type: DataTypes.STRING,
    allowNull: true
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  createdAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updatedAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  sequelize,
  modelName: 'Achievement',
  tableName: 'achievements',
  timestamps: true
});

export default Achievement; 