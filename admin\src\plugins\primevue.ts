import type { App } from 'vue'
import PrimeVue from 'primevue/config'
import ToastService from 'primevue/toastservice'
import ConfirmationService from 'primevue/confirmationservice'
import DialogService from 'primevue/dialogservice'

// Import PrimeVue components
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import Dialog from 'primevue/dialog'
import Toast from 'primevue/toast'
import Card from 'primevue/card'
import Menu from 'primevue/menu'
import Menubar from 'primevue/menubar'
import Dropdown from 'primevue/dropdown'
import Calendar from 'primevue/calendar'
import Checkbox from 'primevue/checkbox'
import RadioButton from 'primevue/radiobutton'
import InputSwitch from 'primevue/inputswitch'
import Textarea from 'primevue/textarea'
import Panel from 'primevue/panel'
import FileUpload from 'primevue/fileupload'
import ProgressBar from 'primevue/progressbar'
import TabView from 'primevue/tabview'
import TabPanel from 'primevue/tabpanel'
import Accordion from 'primevue/accordion'
import AccordionTab from 'primevue/accordiontab'
import ConfirmDialog from 'primevue/confirmdialog'
import Sidebar from 'primevue/sidebar'
import Paginator from 'primevue/paginator'
import Chart from 'primevue/chart'
import ProgressSpinner from 'primevue/progressspinner'
import MultiSelect from 'primevue/multiselect'
import Breadcrumb from 'primevue/breadcrumb'
import Badge from 'primevue/badge'
import Avatar from 'primevue/avatar'
import Divider from 'primevue/divider'
import Message from 'primevue/message'
import InlineMessage from 'primevue/inlinemessage'
import Tag from 'primevue/tag'
import Tooltip from 'primevue/tooltip'

// Import PrimeIcons and PrimeFlex
import 'primeicons/primeicons.css'
import 'primeflex/primeflex.css'

// Tailwind preset for PrimeVue
const tailwindPreset = {
  button: {
    root: ({ props }: any) => ({
      className: [
        // Base
        'relative inline-flex items-center justify-center cursor-pointer',
        'text-center align-bottom no-underline overflow-hidden',
        'transition duration-200 ease-in-out',
        'focus:outline-none focus:outline-offset-0 focus:shadow-[0_0_0_2px_rgba(255,255,255,1),0_0_0_4px_rgba(59,130,246,0.5)]',
        'border border-solid',
        
        // Shape
        props.rounded ? 'rounded-full' : 'rounded-md',
        
        // Size
        props.size === 'small' ? 'text-sm py-1 px-2' : props.size === 'large' ? 'text-lg py-3 px-5' : 'text-base py-2 px-4',
        
        // Severity
        props.severity === null && !props.text && !props.outlined && 'bg-blue-500 border-blue-500 text-white hover:bg-blue-600 hover:border-blue-600',
        props.severity === 'secondary' && !props.text && !props.outlined && 'bg-gray-500 border-gray-500 text-white hover:bg-gray-600 hover:border-gray-600',
        props.severity === 'success' && !props.text && !props.outlined && 'bg-green-500 border-green-500 text-white hover:bg-green-600 hover:border-green-600',
        props.severity === 'info' && !props.text && !props.outlined && 'bg-blue-500 border-blue-500 text-white hover:bg-blue-600 hover:border-blue-600',
        props.severity === 'warning' && !props.text && !props.outlined && 'bg-amber-500 border-amber-500 text-white hover:bg-amber-600 hover:border-amber-600',
        props.severity === 'danger' && !props.text && !props.outlined && 'bg-red-500 border-red-500 text-white hover:bg-red-600 hover:border-red-600',
        
        // Text
        props.text && 'bg-transparent border-transparent',
        props.text && props.severity === null && 'text-blue-500 hover:bg-blue-50',
        props.text && props.severity === 'secondary' && 'text-gray-500 hover:bg-gray-50',
        props.text && props.severity === 'success' && 'text-green-500 hover:bg-green-50',
        props.text && props.severity === 'info' && 'text-blue-500 hover:bg-blue-50',
        props.text && props.severity === 'warning' && 'text-amber-500 hover:bg-amber-50',
        props.text && props.severity === 'danger' && 'text-red-500 hover:bg-red-50',
        
        // Outlined
        props.outlined && 'bg-transparent',
        props.outlined && props.severity === null && 'text-blue-500 border-blue-500 hover:bg-blue-50',
        props.outlined && props.severity === 'secondary' && 'text-gray-500 border-gray-500 hover:bg-gray-50',
        props.outlined && props.severity === 'success' && 'text-green-500 border-green-500 hover:bg-green-50',
        props.outlined && props.severity === 'info' && 'text-blue-500 border-blue-500 hover:bg-blue-50',
        props.outlined && props.severity === 'warning' && 'text-amber-500 border-amber-500 hover:bg-amber-50',
        props.outlined && props.severity === 'danger' && 'text-red-500 border-red-500 hover:bg-red-50',
        
        // Disabled
        props.disabled && 'opacity-60 cursor-default pointer-events-none'
      ]
    }),
    label: 'font-bold',
    icon: ({ props }: any) => ({
      className: [
        'mr-2',
        props.iconPos === 'right' && 'order-1 ml-2 mr-0'
      ]
    })
  },
  card: {
    root: 'bg-white border border-gray-200 rounded-lg shadow-md',
    body: 'p-5',
    title: 'text-xl font-bold text-gray-800 mb-3',
    subtitle: 'text-gray-600 mb-4',
    content: 'py-4',
    footer: 'pt-4 border-t border-gray-200'
  },
  inputtext: {
    root: 'block w-full px-3 py-2 text-base font-normal text-gray-700 bg-white bg-clip-padding border border-solid border-gray-300 rounded-md transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-500 focus:outline-none'
  },
  datatable: {
    root: 'relative',
    loadingOverlay: 'absolute flex items-center justify-center z-10 bg-gray-100/70 transition-all',
    loadingIcon: 'w-8 h-8',
    wrapper: 'overflow-x-auto',
    header: 'bg-white border-b border-gray-300 p-4 font-bold',
    footer: 'bg-white border-t border-gray-300 p-4',
    table: 'w-full border-collapse',
    thead: 'bg-gray-50',
    tbody: 'bg-white',
    tfoot: 'bg-gray-50',
    headerRow: 'border-b border-gray-300',
    bodyRow: 'border-b border-gray-200 transition-all hover:bg-gray-100',
    footerRow: 'border-t border-gray-300',
    headerCell: 'text-left p-3 font-semibold',
    bodyCell: 'text-left p-3',
    footerCell: 'text-left p-3 font-semibold'
  },
  dialog: {
    root: ({ state }: any) => ({
      className: [
        'bg-white rounded-lg shadow-lg border border-gray-300',
        'flex flex-col',
        'max-h-90vh',
        state.maximized ? 'h-screen w-screen' : 'transform scale-100'
      ]
    }),
    header: 'flex items-center justify-between p-4 border-b border-gray-300',
    title: 'font-bold text-lg',
    closeButton: 'flex items-center justify-center rounded-full w-8 h-8 transition hover:bg-gray-100',
    closeIcon: 'text-gray-500',
    content: 'p-4 overflow-y-auto',
    footer: 'flex items-center justify-end p-4 border-t border-gray-300',
    mask: 'fixed top-0 left-0 w-full h-full flex items-center justify-center bg-black bg-opacity-40'
  }
};

export default {
  install: (app: App) => {
    // Register PrimeVue and its services with unstyled mode enabled
    app.use(PrimeVue, { 
      ripple: true,
      unstyled: true,
      pt: tailwindPreset
    })
    app.use(ToastService)
    app.use(ConfirmationService)
    app.use(DialogService)

    // Register components globally
    app.component('Button', Button)
    app.component('InputText', InputText)
    app.component('DataTable', DataTable)
    app.component('Column', Column)
    app.component('Dialog', Dialog)
    app.component('Toast', Toast)
    app.component('Card', Card)
    app.component('Menu', Menu)
    app.component('Menubar', Menubar)
    app.component('Dropdown', Dropdown)
    app.component('Calendar', Calendar)
    app.component('Checkbox', Checkbox)
    app.component('RadioButton', RadioButton)
    app.component('InputSwitch', InputSwitch)
    app.component('Textarea', Textarea)
    app.component('Panel', Panel)
    app.component('FileUpload', FileUpload)
    app.component('ProgressBar', ProgressBar)
    app.component('TabView', TabView)
    app.component('TabPanel', TabPanel)
    app.component('Accordion', Accordion)
    app.component('AccordionTab', AccordionTab)
    app.component('ConfirmDialog', ConfirmDialog)
    app.component('Sidebar', Sidebar)
    app.component('Paginator', Paginator)
    app.component('Chart', Chart)
    app.component('ProgressSpinner', ProgressSpinner)
    app.component('MultiSelect', MultiSelect)
    app.component('Breadcrumb', Breadcrumb)
    app.component('Badge', Badge)
    app.component('Avatar', Avatar)
    app.component('Divider', Divider)
    app.component('Message', Message)
    app.component('InlineMessage', InlineMessage)
    app.component('Tag', Tag)

    // Register directives
    app.directive('tooltip', Tooltip)
  }
} 