import { DataTypes, Model } from 'sequelize';
import { sequelize } from '../config/database.js';

class Quiz extends Model {}

Quiz.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  topicId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'grammar_topics',
      key: 'id'
    }
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  timeLimit: {
    type: DataTypes.INTEGER, // in minutes
    allowNull: false,
    defaultValue: 15
  },
  passingScore: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 70,
    validate: {
      min: 0,
      max: 100
    }
  },
  difficulty: {
    type: DataTypes.ENUM('beginner', 'intermediate', 'advanced'),
    allowNull: false,
    defaultValue: 'beginner'
  },
  questions: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: 'JSON string containing quiz questions and answers'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  createdAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updatedAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  sequelize,
  modelName: 'Quiz',
  tableName: 'quizzes',
  timestamps: true
});

export default Quiz; 