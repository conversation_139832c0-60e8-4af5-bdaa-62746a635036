<template>
  <header class="bg-white dark:bg-dark-card shadow-md sticky top-0 z-50 transition-colors duration-300">
    <nav class="py-4">
      <div class="container mx-auto px-4 flex justify-between items-center">
        <div class="logo">
          <router-link to="/" class="flex items-center">
            <span class="text-2xl font-bold text-gray-800 dark:text-white">English<span class="text-primary">Grammar</span></span>
          </router-link>
        </div>
        
        <div class="md:hidden flex flex-col justify-between w-8 h-6 cursor-pointer" @click="toggleMobileMenu">
          <span class="h-0.5 w-full bg-gray-800 dark:bg-white rounded-full transition-all duration-300"></span>
          <span class="h-0.5 w-full bg-gray-800 dark:bg-white rounded-full transition-all duration-300"></span>
          <span class="h-0.5 w-full bg-gray-800 dark:bg-white rounded-full transition-all duration-300"></span>
        </div>
        
        <div class="md:flex items-center gap-6 hidden md:visible">
          <router-link to="/" class="text-gray-800 dark:text-gray-200 font-medium hover:text-primary transition-colors duration-300 relative after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-primary after:transition-all hover:after:w-full">Home</router-link>
          <router-link to="/topics" class="text-gray-800 dark:text-gray-200 font-medium hover:text-primary transition-colors duration-300 relative after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-primary after:transition-all hover:after:w-full">Topics</router-link>
          
          <div class="relative" v-if="!authStore.isAuthenticated">
            <button @click.stop="toggleLevelsMenu" class="dropdown-toggle flex items-center gap-2 text-gray-800 dark:text-gray-200 font-medium bg-transparent border-none cursor-pointer py-2 hover:text-primary transition-colors duration-300">
              <span>Levels</span>
              <i class="border-solid border-r border-b border-current p-0.5 inline-block rotate-45 transition-transform duration-300" :class="{'rotate-225': isLevelsMenuOpen}"></i>
            </button>
            <div v-if="isLevelsMenuOpen" class="dropdown-menu absolute top-full left-0 bg-white dark:bg-dark-card min-w-[180px] shadow-md rounded-lg py-2 z-10">
              <router-link to="/topics/beginner" class="block py-3 px-4 text-gray-800 dark:text-gray-200 hover:bg-primary-light dark:hover:bg-gray-700 hover:text-primary dark:hover:text-primary transition-colors duration-300">Beginner</router-link>
              <router-link to="/topics/intermediate" class="block py-3 px-4 text-gray-800 dark:text-gray-200 hover:bg-primary-light dark:hover:bg-gray-700 hover:text-primary dark:hover:text-primary transition-colors duration-300">Intermediate</router-link>
              <router-link to="/topics/advanced" class="block py-3 px-4 text-gray-800 dark:text-gray-200 hover:bg-primary-light dark:hover:bg-gray-700 hover:text-primary dark:hover:text-primary transition-colors duration-300">Advanced</router-link>
            </div>
          </div>
          
          <template v-if="authStore.isAuthenticated">
            <router-link to="/progress" class="text-gray-800 dark:text-gray-200 font-medium hover:text-primary transition-colors duration-300 relative after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-primary after:transition-all hover:after:w-full">My Progress</router-link>
            <div class="relative">
              <button @click.stop="toggleAccountMenu" class="dropdown-toggle flex items-center gap-2 text-gray-800 dark:text-gray-200 font-medium bg-transparent border-none cursor-pointer py-2 hover:text-primary transition-colors duration-300">
                <span>Account</span>
                <i class="border-solid border-r border-b border-current p-0.5 inline-block rotate-45 transition-transform duration-300" :class="{'rotate-225': isAccountMenuOpen}"></i>
              </button>
              <div v-if="isAccountMenuOpen" class="dropdown-menu absolute top-full left-0 bg-white dark:bg-dark-card min-w-[180px] shadow-md rounded-lg py-2 z-10">
                <router-link to="/profile" class="block py-3 px-4 text-gray-800 dark:text-gray-200 hover:bg-primary-light dark:hover:bg-gray-700 hover:text-primary dark:hover:text-primary transition-colors duration-300">Profile</router-link>
                <a href="#" class="block py-3 px-4 text-gray-800 dark:text-gray-200 hover:bg-primary-light dark:hover:bg-gray-700 hover:text-primary dark:hover:text-primary transition-colors duration-300" @click.prevent="logout">Logout</a>
              </div>
            </div>
          </template>
          
          <template v-else>
            <router-link to="/login" class="px-4 py-2 rounded-lg border border-primary text-primary hover:bg-primary-light hover:text-primary transition-all duration-300">Login</router-link>
            <router-link to="/register" class="px-4 py-2 rounded-lg bg-primary text-white hover:bg-primary-dark hover:text-white transition-all duration-300">Register</router-link>
          </template>
          
          <!-- Thêm nút chuyển đổi theme -->
          <ThemeToggle />
        </div>
        
        <!-- Mobile menu -->
        <div class="absolute top-full left-0 right-0 bg-white dark:bg-dark-card flex-col items-start px-4 py-4 shadow-md md:hidden opacity-0 invisible -translate-y-2.5 transition-all duration-300"
             :class="{ 'opacity-100 visible translate-y-0': mobileMenuOpen }">
          <router-link to="/" class="text-gray-800 dark:text-gray-200 font-medium hover:text-primary transition-colors duration-300 py-2">Home</router-link>
          <router-link to="/topics" class="text-gray-800 dark:text-gray-200 font-medium hover:text-primary transition-colors duration-300 py-2">Topics</router-link>
          
          <div class="w-full py-2" v-if="!authStore.isAuthenticated">
            <button @click.stop="toggleMobileLevelsMenu" class="flex items-center gap-2 text-gray-800 dark:text-gray-200 font-medium bg-transparent border-none cursor-pointer py-2 hover:text-primary transition-colors duration-300">
              <span>Levels</span>
              <i class="border-solid border-r border-b border-current p-0.5 inline-block" :class="{'rotate-225': isMobileLevelsMenuOpen, 'rotate-45': !isMobileLevelsMenuOpen}"></i>
            </button>
            <div class="pl-4" v-if="isMobileLevelsMenuOpen">
              <router-link to="/topics/beginner" class="block py-3 text-gray-800 dark:text-gray-200 hover:text-primary dark:hover:text-primary transition-colors duration-300">Beginner</router-link>
              <router-link to="/topics/intermediate" class="block py-3 text-gray-800 dark:text-gray-200 hover:text-primary dark:hover:text-primary transition-colors duration-300">Intermediate</router-link>
              <router-link to="/topics/advanced" class="block py-3 text-gray-800 dark:text-gray-200 hover:text-primary dark:hover:text-primary transition-colors duration-300">Advanced</router-link>
            </div>
          </div>
          
          <template v-if="authStore.isAuthenticated">
            <router-link to="/progress" class="text-gray-800 dark:text-gray-200 font-medium hover:text-primary transition-colors duration-300 py-2">My Progress</router-link>
            <div class="w-full py-2">
              <button @click.stop="toggleMobileAccountMenu" class="flex items-center gap-2 text-gray-800 dark:text-gray-200 font-medium bg-transparent border-none cursor-pointer py-2 hover:text-primary transition-colors duration-300">
                <span>Account</span>
                <i class="border-solid border-r border-b border-current p-0.5 inline-block" :class="{'rotate-225': isMobileAccountMenuOpen, 'rotate-45': !isMobileAccountMenuOpen}"></i>
              </button>
              <div class="pl-4" v-if="isMobileAccountMenuOpen">
                <router-link to="/profile" class="block py-3 text-gray-800 dark:text-gray-200 hover:text-primary dark:hover:text-primary transition-colors duration-300">Profile</router-link>
                <a href="#" class="block py-3 text-gray-800 dark:text-gray-200 hover:text-primary dark:hover:text-primary transition-colors duration-300" @click.prevent="logout">Logout</a>
              </div>
            </div>
          </template>
          
          <template v-else>
            <router-link to="/login" class="w-full text-center px-4 py-2 rounded-lg border border-primary text-primary hover:bg-primary-light hover:text-primary transition-all duration-300 mt-2">Login</router-link>
            <router-link to="/register" class="w-full text-center px-4 py-2 rounded-lg bg-primary text-white hover:bg-primary-dark hover:text-white transition-all duration-300 mt-2">Register</router-link>
          </template>
          
          <!-- Thêm nút chuyển đổi theme cho mobile -->
          <div class="flex justify-center w-full mt-4">
            <ThemeToggle />
          </div>
        </div>
      </div>
    </nav>
  </header>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { toastService } from '../../services/toast.service';
import { useAuthStore } from '../../stores/auth';
// @ts-ignore
import ThemeToggle from '../ThemeToggle.vue';

defineOptions({
  name: 'HeaderComponent'
});

const router = useRouter();
const mobileMenuOpen = ref(false);
const authStore = useAuthStore();

// Trạng thái menu dropdown desktop
const isAccountMenuOpen = ref(false);
const isLevelsMenuOpen = ref(false);

// Trạng thái menu dropdown mobile
const isMobileAccountMenuOpen = ref(false);
const isMobileLevelsMenuOpen = ref(false);

// Toggle menu mobile
const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value;
  // Đóng tất cả menu con khi đóng menu mobile
  if (!mobileMenuOpen.value) {
    isMobileAccountMenuOpen.value = false;
    isMobileLevelsMenuOpen.value = false;
  }
};

// Toggle menu dropdown desktop
const toggleAccountMenu = () => {
  isAccountMenuOpen.value = !isAccountMenuOpen.value;
  // Đóng menu Levels nếu đang mở
  if (isAccountMenuOpen.value) {
    isLevelsMenuOpen.value = false;
  }
};

const toggleLevelsMenu = () => {
  isLevelsMenuOpen.value = !isLevelsMenuOpen.value;
  // Đóng menu Account nếu đang mở
  if (isLevelsMenuOpen.value) {
    isAccountMenuOpen.value = false;
  }
};

// Toggle menu dropdown mobile
const toggleMobileAccountMenu = () => {
  isMobileAccountMenuOpen.value = !isMobileAccountMenuOpen.value;
  // Đóng menu Levels nếu đang mở
  if (isMobileAccountMenuOpen.value) {
    isMobileLevelsMenuOpen.value = false;
  }
};

const toggleMobileLevelsMenu = () => {
  isMobileLevelsMenuOpen.value = !isMobileLevelsMenuOpen.value;
  // Đóng menu Account nếu đang mở
  if (isMobileLevelsMenuOpen.value) {
    isMobileAccountMenuOpen.value = false;
  }
};

// Thêm event listener khi component được mount
onMounted(() => {
  // Sử dụng setTimeout để đảm bảo sự kiện click không được kích hoạt ngay lập tức
  const handleClickOutside = (event: MouseEvent) => {
    // Chỉ đóng dropdown nếu click không phải vào dropdown hoặc nút toggle
    if (event.target instanceof Element) {
      const isDropdownButton = event.target.closest('.dropdown-toggle');
      const isInsideDropdown = event.target.closest('.dropdown-menu');
      
      if (!isDropdownButton && !isInsideDropdown) {
        isAccountMenuOpen.value = false;
        isLevelsMenuOpen.value = false;
      }
    }
  };
  
  setTimeout(() => {
    document.body.addEventListener('click', handleClickOutside);
  }, 100);
  
  // Lưu hàm xử lý để có thể xóa khi unmount
  onUnmounted(() => {
    document.body.removeEventListener('click', handleClickOutside);
  });
});

const logout = async () => {
  try {
    await authStore.logout();
    toastService.success('Logged out successfully');
    router.push('/login');
    // Đóng menu sau khi logout
    isAccountMenuOpen.value = false;
    isMobileAccountMenuOpen.value = false;
  } catch (error) {
    toastService.error('Failed to logout');
  }
};
</script> 