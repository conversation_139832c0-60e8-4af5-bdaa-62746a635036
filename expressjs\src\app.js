import express from 'express';
import cors from 'cors';
import morgan from 'morgan';
import helmet from 'helmet';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import dotenv from 'dotenv';
import { sequelize, testConnection } from './config/database.js';
import routes from './routes/index.js';
import { globalErrorHandler } from './utils/error-handler.js';
  
// Load environment variables
dotenv.config();

// Create Express app
const app = express();

// Debug middleware for requests - must be first to see raw request
app.use((req, res, next) => {
  console.log(`${req.method} ${req.url}`);
  console.log('Headers:', req.headers['content-type']);
  
  // Collect raw body data for debugging
  let data = '';
  req.on('data', chunk => {
    data += chunk;
  });
  
  req.on('end', () => {
    if (data) {
      console.log('Raw request body:', data);
      try {
        // Try to parse as JSON if content-type is application/json
        if (req.headers['content-type'] && req.headers['content-type'].includes('application/json')) {
          console.log('Parsed JSON:', JSON.parse(data));
        }
      } catch (e) {
        console.log('Failed to parse JSON:', e.message);
      }
    }
  });
  
  next();
});

// CORS configuration
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:5173',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use(helmet({
  crossOriginResourcePolicy: false // Allow serving uploaded files
}));
app.use(compression());
app.use(morgan('dev'));

// Body parsing middleware - make sure these come before routes
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());

// Serve static files from uploads directory
app.use('/uploads', express.static('uploads'));

// Root route
app.get('/', (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Welcome to Express.js MVC API',
    documentation: '/api/health'
  });
});

// Routes
app.use('/api', routes);

// 404 handler for undefined routes
app.all('*', (req, res, next) => {
  res.status(404).json({
    status: 'error',
    message: `Cannot find ${req.originalUrl} on this server!`
  });
});

// Global error handling middleware
app.use(globalErrorHandler);

// Start server
const PORT = process.env.PORT || 3000;

// Test database connection first
testConnection()
  .then(() => {
    // Sync database with safer options
    return sequelize.sync({ 
      // Disable alter in production for safety
      alter: false,
      // Never force in production
      force: false
    });
  })
  .then(() => {
    app.listen(PORT, () => {
      console.log(`Server running on port ${PORT}`);
    });
  })
  .catch(err => {
    console.error('Unable to connect to the database:', err);
  });

export default app;
