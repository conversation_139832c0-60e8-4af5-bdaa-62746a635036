<template>
  <div class="max-w-6xl mx-auto px-4">
    <section class="flex flex-col md:flex-row items-center justify-between gap-8 py-16 min-h-[60vh]">
      <div class="flex-1">
        <h1 class="text-4xl md:text-5xl font-extrabold leading-tight mb-6 bg-gradient-to-r from-blue-600 to-cyan-400 bg-clip-text text-transparent">
          Master English Grammar <span class="text-blue-600">Effortlessly</span>
        </h1>
        <p class="text-xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">Interactive lessons and exercises designed to improve your English proficiency</p>
        <div class="flex gap-4">
          <router-link to="/topics" class="px-6 py-3 rounded-lg font-semibold text-white bg-gradient-to-r from-blue-600 to-cyan-400 shadow-md hover:shadow-lg hover:-translate-y-1 transition-all duration-300">Get Started</router-link>
          <router-link to="/about" class="px-6 py-3 rounded-lg font-semibold text-blue-600 bg-white dark:bg-gray-800 border border-blue-600 hover:bg-blue-50 dark:hover:bg-gray-700 transition-all duration-300 dark:text-blue-400">Learn More</router-link>
        </div>
      </div>
      <div class="flex-1 flex justify-center">
        <div class="w-[300px] h-[300px] bg-gradient-to-br from-gray-100 to-blue-100 dark:from-gray-800 dark:to-blue-900 rounded-2xl flex items-center justify-center text-blue-600">
          <svg xmlns="http://www.w3.org/2000/svg" width="240" height="240" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
            <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path>
            <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path>
            <path d="M8 7h8"></path>
            <path d="M8 11h6"></path>
            <path d="M8 15h4"></path>
          </svg>
        </div>
      </div>
    </section>
    
    <section class="py-16">
      <h2 class="text-4xl font-bold text-center mb-2 bg-gradient-to-r from-blue-600 to-cyan-400 bg-clip-text text-transparent">Choose Your Learning Path</h2>
      <p class="text-center text-gray-600 dark:text-gray-300 mb-12">Select the level that matches your current proficiency</p>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <router-link to="/topics/beginner" class="bg-white dark:bg-dark-card p-8 rounded-xl shadow-md hover:shadow-xl hover:-translate-y-2 transition-all duration-300 border-t-4 border-green-500 text-left">
          <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center text-green-500 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
              <path d="M2 17l10 5 10-5"></path>
              <path d="M2 12l10 5 10-5"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-2 dark:text-gray-100">Beginner</h3>
          <p class="text-gray-600 dark:text-gray-400">Perfect for those just starting their English journey</p>
        </router-link>
        
        <router-link to="/topics/intermediate" class="bg-white dark:bg-dark-card p-8 rounded-xl shadow-md hover:shadow-xl hover:-translate-y-2 transition-all duration-300 border-t-4 border-blue-500 text-left">
          <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center text-blue-500 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M18 8h1a4 4 0 0 1 0 8h-1"></path>
              <path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"></path>
              <line x1="6" y1="1" x2="6" y2="4"></line>
              <line x1="10" y1="1" x2="10" y2="4"></line>
              <line x1="14" y1="1" x2="14" y2="4"></line>
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-2 dark:text-gray-100">Intermediate</h3>
          <p class="text-gray-600 dark:text-gray-400">Build upon your foundation with more complex concepts</p>
        </router-link>
        
        <router-link to="/topics/advanced" class="bg-white dark:bg-dark-card p-8 rounded-xl shadow-md hover:shadow-xl hover:-translate-y-2 transition-all duration-300 border-t-4 border-purple-500 text-left">
          <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center text-purple-500 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-2 dark:text-gray-100">Advanced</h3>
          <p class="text-gray-600 dark:text-gray-400">Perfect your grammar with expert-level topics</p>
        </router-link>
      </div>
    </section>
    
    <section class="py-16 bg-gray-50 dark:bg-gray-800 rounded-3xl px-6 my-8">
      <h2 class="text-4xl font-bold text-center mb-12 bg-gradient-to-r from-blue-600 to-cyan-400 bg-clip-text text-transparent">Why Learn With Us</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div class="bg-white dark:bg-dark-card p-6 rounded-xl shadow-md hover:shadow-lg hover:-translate-y-1 transition-all duration-300">
          <div class="w-12 h-12 bg-blue-50 dark:bg-blue-900 rounded-lg flex items-center justify-center text-blue-600 dark:text-blue-400 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
              <line x1="16" y1="13" x2="8" y2="13"></line>
              <line x1="16" y1="17" x2="8" y2="17"></line>
              <polyline points="10 9 9 9 8 9"></polyline>
            </svg>
          </div>
          <h3 class="text-xl font-bold mb-2 dark:text-gray-100">Structured Learning</h3>
          <p class="text-gray-600 dark:text-gray-400 leading-relaxed">Carefully designed lessons that build upon each other</p>
        </div>
        
        <div class="bg-white dark:bg-dark-card p-6 rounded-xl shadow-md hover:shadow-lg hover:-translate-y-1 transition-all duration-300">
          <div class="w-12 h-12 bg-blue-50 dark:bg-blue-900 rounded-lg flex items-center justify-center text-blue-600 dark:text-blue-400 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <polyline points="12 6 12 12 16 14"></polyline>
            </svg>
          </div>
          <h3 class="text-xl font-bold mb-2 dark:text-gray-100">Learn at Your Pace</h3>
          <p class="text-gray-600 dark:text-gray-400 leading-relaxed">Study whenever and wherever you want</p>
        </div>
        
        <div class="bg-white dark:bg-dark-card p-6 rounded-xl shadow-md hover:shadow-lg hover:-translate-y-1 transition-all duration-300">
          <div class="w-12 h-12 bg-blue-50 dark:bg-blue-900 rounded-lg flex items-center justify-center text-blue-600 dark:text-blue-400 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 20v-6M6 20V10M18 20V4"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold mb-2 dark:text-gray-100">Track Progress</h3>
          <p class="text-gray-600 dark:text-gray-400 leading-relaxed">Monitor your improvement with detailed statistics</p>
        </div>
      </div>
      
      <div class="mt-12 text-center">
        <router-link to="/topics" class="px-6 py-3 rounded-lg font-semibold text-white bg-gradient-to-r from-blue-600 to-cyan-400 shadow-md hover:shadow-lg hover:-translate-y-1 transition-all duration-300">Browse All Topics</router-link>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// Home view component
</script> 