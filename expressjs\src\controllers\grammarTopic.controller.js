import { grammarTopicService } from '../services/index.js';
import BaseController from './base.controller.js';

class GrammarTopicController extends BaseController {
  constructor() {
    super();
  }

  // Get all active grammar topics
  async getAllTopics(req, res) {
    try {
      const topics = await grammarTopicService.getAllActiveTopics();
      
      return res.status(200).json({
        success: true,
        data: topics
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: 'Error retrieving grammar topics',
        error: error.message
      });
    }
  }

  // Get topics by level (beginner, intermediate, advanced)
  async getTopicsByLevel(req, res) {
    try {
      const { level } = req.params;
      const topics = await grammarTopicService.getTopicsByLevel(level);
      
      return res.status(200).json({
        success: true,
        data: topics
      });
    } catch (error) {
      if (error.message.includes('Invalid level')) {
        return res.status(400).json({
          success: false,
          message: error.message
        });
      }
      
      return res.status(500).json({
        success: false,
        message: 'Error retrieving grammar topics by level',
        error: error.message
      });
    }
  }

  // Get topic with its lessons
  async getTopicWithLessons(req, res) {
    try {
      const { id } = req.params;
      const topic = await grammarTopicService.getTopicWithLessons(id);
      
      return res.status(200).json({
        success: true,
        data: topic
      });
    } catch (error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: error.message
        });
      }
      
      return res.status(500).json({
        success: false,
        message: 'Error retrieving grammar topic with lessons',
        error: error.message
      });
    }
  }
}

export default new GrammarTopicController(); 