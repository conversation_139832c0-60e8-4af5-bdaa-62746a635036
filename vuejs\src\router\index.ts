import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('../views/HomeView.vue')
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/auth/LoginView.vue')
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/auth/RegisterView.vue')
    },
    {
      path: '/topics',
      name: 'topics',
      component: () => import('../views/grammar/TopicsView.vue')
    },
    {
      path: '/topics/:level',
      name: 'topics-by-level',
      component: () => import('../views/grammar/TopicsByLevelView.vue')
    },
    {
      path: '/topics/:id/lessons',
      name: 'topic-lessons',
      component: () => import('../views/grammar/TopicLessonsView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/lessons/:id',
      name: 'lesson-detail',
      component: () => import('../views/grammar/LessonDetailView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/lessons/:id/exercises',
      name: 'lesson-exercises',
      component: () => import('../views/grammar/LessonExercisesView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/auth/ProfileView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/progress',
      name: 'progress',
      component: () => import('../views/grammar/UserProgressView.vue'),
      meta: { requiresAuth: true }
    }
  ],
})

// Navigation guard
router.beforeEach((to, from, next) => {
  // Kiểm tra accessToken trong localStorage
  const accessToken = localStorage.getItem('accessToken')
  const isAuthenticated = !!accessToken
  
  if (to.meta.requiresAuth && !isAuthenticated) {
    console.log(`Access denied to ${to.path}, redirecting to login`)
    // Redirect to login with a message and the intended destination
    next({ 
      name: 'login', 
      query: { 
        redirect: to.fullPath,
        message: 'Please login to access this content' 
      } 
    })
  } else {
    next()
  }
})

export default router
