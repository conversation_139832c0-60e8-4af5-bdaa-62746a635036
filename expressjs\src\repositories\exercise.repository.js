import BaseRepository from './base.repository.js';
import { Exercise } from '../models/index.js';

class ExerciseRepository extends BaseRepository {
  constructor() {
    super(Exercise);
  }

  /**
   * Find exercises by lesson ID
   * @param {number} lessonId - Lesson ID
   * @returns {Promise<Array>} - Array of exercises
   */
  async findByLessonId(lessonId) {
    return this.model.findAll({
      where: { 
        lessonId,
        isActive: true 
      },
      order: [['order', 'ASC']]
    });
  }

  /**
   * Find exercises by difficulty
   * @param {string} difficulty - Difficulty level (easy, medium, hard)
   * @returns {Promise<Array>} - Array of exercises
   */
  async findByDifficulty(difficulty) {
    return this.model.findAll({
      where: { 
        difficulty,
        isActive: true 
      }
    });
  }
}

export default new ExerciseRepository(); 